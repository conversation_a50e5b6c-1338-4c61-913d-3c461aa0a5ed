package net.netca.cloudkeyserver.changjiangca.constant;

/**
 * 长江CA错误码枚举
 *
 * 定义长江CA平台对接过程中的标准化错误码，
 * 提供错误码到用户友好消息的映射关系。
 *
 * 错误码命名规范：
 * - CJCA_CONFIG_xxx: 配置相关错误
 * - CJCA_CONNECTION_xxx: 连接相关错误
 * - CJCA_CMP_xxx: CMP协议相关错误
 * - CJCA_CERT_xxx: 证书处理相关错误
 * - CJCA_TEMPLATE_xxx: 模板相关错误
 * - CJCA_SECURITY_xxx: 安全相关错误
 *
 * <AUTHOR> Team
 * @since 2.42.1
 */
public enum CJCAErrorCode {

    // ==================== 通用错误 ====================
    UNKNOWN_ERROR("CJCA_000", "未知错误"),
    SYSTEM_ERROR("CJCA_001", "系统内部错误"),
    OPERATION_NOT_SUPPORTED("CJCA_002", "操作不支持"),

    // ==================== 配置相关错误 ====================
    CONFIG_MISSING("CJCA_100", "配置项缺失"),
    CONFIG_INVALID("CJCA_101", "配置项无效"),
    CONFIG_FORMAT_ERROR("CJCA_102", "配置格式错误"),
    CONFIG_VALIDATION_FAILED("CJCA_103", "配置验证失败"),

    // 证书配置错误
    CERT_CONFIG_ERROR("CJCA_110", "证书配置错误"),
    CA_CERT_INVALID("CJCA_111", "CA证书无效"),
    RA_CERT_INVALID("CJCA_112", "RA证书无效"),
    THIRD_PARTY_CERT_INVALID("CJCA_113", "第三方证书无效"),
    PRIVATE_KEY_INVALID("CJCA_114", "私钥无效"),
    CERT_PARSE_ERROR("CJCA_115", "证书解析错误"),

    // 算法配置错误
    ALGORITHM_CONFIG_ERROR("CJCA_120", "算法配置错误"),
    UNSUPPORTED_ALGORITHM("CJCA_121", "不支持的算法"),
    ALGORITHM_MISMATCH("CJCA_122", "算法不匹配"),

    // 超时配置错误
    TIMEOUT_CONFIG_ERROR("CJCA_130", "超时配置错误"),
    INVALID_TIMEOUT_VALUE("CJCA_131", "无效的超时值"),

    // ==================== 连接相关错误 ====================
    CONNECTION_ERROR("CJCA_200", "连接错误"),
    NETWORK_ERROR("CJCA_201", "网络连接失败"),
    CONNECTION_TIMEOUT("CJCA_202", "连接超时"),
    READ_TIMEOUT("CJCA_203", "读取超时"),

    // HTTP相关错误
    HTTP_ERROR("CJCA_210", "HTTP请求错误"),
    HTTP_BAD_REQUEST("CJCA_211", "HTTP请求格式错误"),
    HTTP_UNAUTHORIZED("CJCA_212", "HTTP认证失败"),
    HTTP_FORBIDDEN("CJCA_213", "HTTP访问被禁止"),
    HTTP_NOT_FOUND("CJCA_214", "HTTP资源未找到"),
    HTTP_SERVER_ERROR("CJCA_215", "HTTP服务器内部错误"),
    HTTP_SERVICE_UNAVAILABLE("CJCA_216", "HTTP服务不可用"),

    // SSL相关错误
    SSL_ERROR("CJCA_220", "SSL连接错误"),
    SSL_HANDSHAKE_FAILED("CJCA_221", "SSL握手失败"),
    SSL_CERT_VERIFICATION_FAILED("CJCA_222", "SSL证书验证失败"),

    // 响应相关错误
    EMPTY_RESPONSE("CJCA_230", "响应为空"),
    INVALID_RESPONSE_FORMAT("CJCA_231", "响应格式无效"),
    RESPONSE_PARSE_ERROR("CJCA_232", "响应解析错误"),

    // ==================== CMP协议相关错误 ====================
    CMP_PROTOCOL_ERROR("CJCA_300", "CMP协议错误"),
    CMP_ENCODE_ERROR("CJCA_301", "CMP消息编码失败"),
    CMP_DECODE_ERROR("CJCA_302", "CMP消息解码失败"),
    CMP_VERSION_MISMATCH("CJCA_303", "CMP协议版本不匹配"),
    CMP_MESSAGE_FORMAT_ERROR("CJCA_304", "CMP消息格式错误"),

    // CMP响应状态错误
    CMP_RESPONSE_STATUS_ERROR("CJCA_310", "CMP响应状态错误"),
    CMP_RESPONSE_REJECTED("CJCA_311", "CMP请求被拒绝"),
    CMP_RESPONSE_PENDING("CJCA_312", "CMP请求处理中"),
    CMP_RESPONSE_FAILED("CJCA_313", "CMP请求处理失败"),

    // CMP消息类型错误
    CMP_UNSUPPORTED_MESSAGE_TYPE("CJCA_320", "不支持的CMP消息类型"),
    CMP_INVALID_MESSAGE_SEQUENCE("CJCA_321", "无效的CMP消息序列"),

    // ==================== 证书处理相关错误 ====================
    CERT_PROCESSING_ERROR("CJCA_400", "证书处理错误"),
    CERT_REQUEST_FAILED("CJCA_401", "证书申请失败"),
    CERT_DOWNLOAD_FAILED("CJCA_402", "证书下载失败"),
    CERT_REVOCATION_FAILED("CJCA_403", "证书注销失败"),
    CERT_STATUS_QUERY_FAILED("CJCA_404", "证书状态查询失败"),

    // 证书状态相关
    CERT_NOT_FOUND("CJCA_410", "证书未找到"),
    CERT_EXPIRED("CJCA_411", "证书已过期"),
    CERT_REVOKED("CJCA_412", "证书已注销"),
    CERT_SUSPENDED("CJCA_413", "证书已暂停"),
    CERT_NOT_YET_VALID("CJCA_414", "证书尚未生效"),

    // 证书验证相关
    CERT_VALIDATION_FAILED("CJCA_420", "证书验证失败"),
    CERT_CHAIN_VALIDATION_FAILED("CJCA_421", "证书链验证失败"),
    CERT_SIGNATURE_VERIFICATION_FAILED("CJCA_422", "证书签名验证失败"),

    // ==================== 模板相关错误 ====================
    TEMPLATE_ERROR("CJCA_500", "证书模板错误"),
    TEMPLATE_NOT_FOUND("CJCA_501", "证书模板未找到"),
    TEMPLATE_CONFIG_ERROR("CJCA_502", "证书模板配置错误"),
    TEMPLATE_VALIDATION_FAILED("CJCA_503", "证书模板验证失败"),
    UNSUPPORTED_TEMPLATE_TYPE("CJCA_504", "不支持的证书模板类型"),

    // ==================== 安全相关错误 ====================
    SECURITY_ERROR("CJCA_600", "安全错误"),
    SIGNATURE_VERIFICATION_FAILED("CJCA_601", "签名验证失败"),
    ENCRYPTION_FAILED("CJCA_602", "加密失败"),
    DECRYPTION_FAILED("CJCA_603", "解密失败"),
    KEY_GENERATION_FAILED("CJCA_604", "密钥生成失败"),

    // 认证相关
    AUTHENTICATION_FAILED("CJCA_610", "认证失败"),
    AUTHORIZATION_FAILED("CJCA_611", "授权失败"),
    ACCESS_DENIED("CJCA_612", "访问被拒绝"),

    // ==================== 业务逻辑错误 ====================
    BUSINESS_ERROR("CJCA_700", "业务逻辑错误"),
    INVALID_REQUEST_PARAMETER("CJCA_701", "无效的请求参数"),
    MISSING_REQUIRED_PARAMETER("CJCA_702", "缺少必需参数"),
    PARAMETER_VALIDATION_FAILED("CJCA_703", "参数验证失败"),

    // 用户相关
    USER_NOT_FOUND("CJCA_710", "用户未找到"),
    USER_ALREADY_EXISTS("CJCA_711", "用户已存在"),
    USER_STATUS_INVALID("CJCA_712", "用户状态无效"),

    // 请求相关
    REQUEST_ID_NOT_FOUND("CJCA_720", "请求ID未找到"),
    REQUEST_ALREADY_PROCESSED("CJCA_721", "请求已处理"),
    REQUEST_EXPIRED("CJCA_722", "请求已过期"),
    DUPLICATE_REQUEST("CJCA_723", "重复请求");

    /** 错误代码 */
    private final String code;

    /** 错误描述 */
    private final String message;

    /**
     * 构造方法
     *
     * @param code 错误代码
     * @param message 错误描述
     */
    CJCAErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 获取错误代码
     *
     * @return 错误代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取错误描述
     *
     * @return 错误描述
     */
    public String getMessage() {
        return message;
    }

    /**
     * 根据错误代码查找对应的错误码枚举
     *
     * @param code 错误代码
     * @return 对应的错误码枚举，如果未找到则返回null
     */
    public static CJCAErrorCode fromCode(String code) {
        if (code == null) {
            return null;
        }

        for (CJCAErrorCode errorCode : values()) {
            if (errorCode.code.equals(code)) {
                return errorCode;
            }
        }

        return null;
    }

    /**
     * 检查是否为配置相关错误
     *
     * @return 如果是配置相关错误则返回true
     */
    public boolean isConfigError() {
        return code.startsWith("CJCA_1");
    }

    /**
     * 检查是否为连接相关错误
     *
     * @return 如果是连接相关错误则返回true
     */
    public boolean isConnectionError() {
        return code.startsWith("CJCA_2");
    }

    /**
     * 检查是否为CMP协议相关错误
     *
     * @return 如果是CMP协议相关错误则返回true
     */
    public boolean isCmpProtocolError() {
        return code.startsWith("CJCA_3");
    }

    /**
     * 检查是否为证书处理相关错误
     *
     * @return 如果是证书处理相关错误则返回true
     */
    public boolean isCertificateError() {
        return code.startsWith("CJCA_4");
    }

    /**
     * 检查是否为模板相关错误
     *
     * @return 如果是模板相关错误则返回true
     */
    public boolean isTemplateError() {
        return code.startsWith("CJCA_5");
    }

    /**
     * 检查是否为安全相关错误
     *
     * @return 如果是安全相关错误则返回true
     */
    public boolean isSecurityError() {
        return code.startsWith("CJCA_6");
    }

    /**
     * 检查是否为业务逻辑错误
     *
     * @return 如果是业务逻辑错误则返回true
     */
    public boolean isBusinessError() {
        return code.startsWith("CJCA_7");
    }

    @Override
    public String toString() {
        return code + ": " + message;
    }
}
