package net.netca.cloudkeyserver.changjiangca.manager;

import cn.hutool.core.util.IdUtil;
import lombok.extern.slf4j.Slf4j;
import net.netca.cloudkey.base.po.AuthorityOperator;
import net.netca.cloudkey.base.po.BusinessCertAttribute;
import net.netca.cloudkey.base.po.BusinessUser;
import net.netca.cloudkey.base.po.ConfigProject;
import net.netca.cloudkey.base.util.DateUtil;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.pojo.*;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.pojo.req.RegisterRequest;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.pojo.resp.NetcaBpmsResponse;
import net.netca.cloudkeyserver.changjiangca.adapter.CJCAResponseAdapter;
import net.netca.cloudkeyserver.changjiangca.config.CJCAConfigManager;
import net.netca.cloudkeyserver.changjiangca.exception.CJCAException;
import net.netca.cloudkeyserver.manager.AbstractCertificateLifecycleManager;
import net.netca.netcasvs.jni.KeyPairWrapper;
import net.netca.sdk.codec.MessageDecoder;
import net.netca.sdk.codec.MessageEncoder;
import net.netca.sdk.codec.cmp.P10CertReqMessageCodec;
import net.netca.sdk.codec.cmp.PollReqMessageCodec;
import net.netca.sdk.codec.cmp.RevocationReqMessageCodec;
import net.netca.sdk.constants.RevokeReasonEnum;
import net.netca.sdk.entity.CmpRespResult;
import net.netca.sdk.entity.SingleCertReqContext;
import net.netca.sdk.message.*;
import net.netca.sdk.message.cmp.CmpMessage;
import net.netca.sdk.message.cmp.P10CertReqMessage;
import net.netca.sdk.message.cmp.PollReqMessage;
import net.netca.sdk.message.cmp.RevocationReqMessage;
import net.netca.sdk.message.cmp.config.CmpMessageConfigManagement;
import okhttp3.*;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.asn1.cmp.PKIMessage;
import org.bouncycastle.asn1.cmp.PKIMessages;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 长江CA证书生命周期管理器
 *
 * 基于netcarasdk实现与长江CA平台的对接，支持：
 * - 基于CMP协议的证书申请、查询、下载、注销
 * - 自动配置和热更新支持
 * - 完整的错误处理和日志记录
 * - 与现有业务逻辑的无缝集成
 * - 使用父类提供的通用密钥对管理功能
 *
 * 注意：
 * - 长江CA实现不支持管理员PIN解密功能，doDecryptAdministratorPin方法将抛出UnsupportedOperationException异常
 * - 密钥对更新和保存功能使用AbstractCertificateLifecycleManager中的通用实现
 * - 如需特殊的密钥对处理逻辑，可重写doUpdateSignKeyPairAndSaveEncKeyPair方法
 *
 * <AUTHOR> Team
 * @since 2.42.1
 */
@Slf4j
@Component("cjcaCertificateLifecycleManager")
@ConditionalOnClass(name = "net.netca.sdk.codec.MessageEncoder")
public class CJCACertificateLifecycleManager extends AbstractCertificateLifecycleManager {

    // ==================== 依赖注入 ====================

    @Autowired
    private CJCAConfigManager configManager;

    @Autowired
    private CJCAResponseAdapter responseAdapter;

    // ==================== ICertificateLifecycleManager 接口方法实现 ====================


    // ==================== 抽象方法实现 ====================

    /**
     * 实现证书申请
     *
     * @param registerRequest 证书申请请求
     * @param opSignature 操作签名
     * @param businessUser 业务用户
     * @param configProject 项目配置
     * @param url 申请URL（长江CA实现中忽略此参数）
     * @return 业务平台响应
     * @throws Exception 申请过程中的异常
     */
    @Override
    protected NetcaBpmsResponse doApplyCertificate(RegisterRequest registerRequest, String opSignature, BusinessUser businessUser, ConfigProject configProject, String url) throws Exception {
        log.info("开始长江CA证书申请，用户ID: {}, 项目ID: {}", businessUser.getId(), configProject.getId());

        try {
            // 1. 构建CMP配置管理对象
            CmpMessageConfigManagement cmpConfig = configManager.createCmpMessageConfigManagement();
            if (cmpConfig == null) {
                throw new CJCAException("CJCA_CONFIG_ERROR", "无法获取CMP配置管理对象");
            }

            // 2. 生成证书请求ID
            long requestId = generateCertificateRequestId();
            log.debug("生成证书请求ID: {}", requestId);

            // 3. 构建用户信息
            UserInfo userInfo = buildUserInfoForCmp(businessUser);

            // 4. 构建证书有效期
            Date now = DateUtil.getNow();
            Cert cert = registerRequest.getCert();
            Integer interval = cert.getInterval();
            if (cert.getEndTime() == null) {
                cert.setEndTime(DateUtil.dateAddMonth(now, interval));
            }

            Validity validity = Validity.builder()
                    .startTime(now)
                    .endTime(cert.getEndTime())
                    .build();

            // 6. 构建自定义扩展信息
            CustomFreeText customFreeText = CustomFreeText.builder().validity(validity).userInfo(userInfo).certReqId(requestId).build();

            // 7. 获取P10证书请求数据
            String p10Base64 = cert.getP10();
            if (!StringUtils.hasText(p10Base64)) {
                throw new CJCAException("CJCA_P10_ERROR", "P10证书请求数据为空");
            }

            // 8. 构建P10证书申请消息
            P10CertReqMessage p10CertReqMessage = P10CertReqMessage.defaultMessage()
                    .toBuilder()
                    .senderStr(cmpConfig.getThirdPartyServerCommCert().getSubject().getLdapName())
                    .recipientStr(cmpConfig.getCommunicationCert().getSubject().getLdapName())
                    .p10Base64(p10Base64)
                    .customFreeText(customFreeText)
                    .certRequestId(requestId)
                    .build();

            log.debug("构建P10证书申请消息完成，发送方: {}, 接收方: {}", p10CertReqMessage.getSenderStr(), p10CertReqMessage.getRecipientStr());

            // 9. 创建编码器和上下文
            MessageEncoder<CmpMessage> encoder = P10CertReqMessageCodec.createEncoder(cmpConfig);
            SingleCertReqContext context = new SingleCertReqContext(p10CertReqMessage);

            // 10. 编码消息
            encoder.encode(context, p10CertReqMessage);
            PKIMessages reqPkiMessages = context.getPKIMessages();
            byte[] reqData = reqPkiMessages.getEncoded();

            log.debug("CMP消息编码完成，请求数据大小: {} bytes", reqData.length);

            // 11. 构建请求URL
            String templateId = configProject.getBpmsCertTemplateId();
            String targetUrl = configManager.buildCmpRequestUrl(templateId);

            log.debug("发送证书申请请求到: {}", targetUrl);

            // 12. 发送HTTP请求并解析响应
            CmpRespResult cmpRespResult = sendCmpRequestAndParseResponse(targetUrl, reqData, cmpConfig, context);

            // 13. 适配响应结果
            NetcaBpmsResponse response = responseAdapter.adaptCertificateApplicationResponse(cmpRespResult);

            log.info("长江CA证书申请完成，请求ID: {}, 状态: {}", requestId, response.getStatus());
            return response;

        } catch (CJCAException e) {
            log.error("长江CA证书申请失败: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("长江CA证书申请过程中发生未知错误", e);
            throw new CJCAException("CJCA_APPLY_ERROR", "证书申请失败: " + e.getMessage(), e);
        }
    }

    /**
     * 实现证书状态查询
     *
     * @param systemId 系统ID
     * @param requestId 请求ID
     * @return 业务平台响应
     * @throws Exception 查询过程中的异常
     */
    @Override
    protected NetcaBpmsResponse doQueryCertificateStatus(String systemId, String requestId) throws Exception {
        log.info("开始长江CA证书状态查询，系统ID: {}, 请求ID: {}", systemId, requestId);

        try {
            // 1. 构建CMP配置管理对象
            CmpMessageConfigManagement cmpConfig = configManager.createCmpMessageConfigManagement();

            // 2. 构建轮询请求消息
            PollReqMessage pollReqMessage = PollReqMessage.defaultMessage().toBuilder().senderStr(cmpConfig.getThirdPartyServerCommCert().getSubject().getLdapName()).recipientStr(cmpConfig.getCommunicationCert().getSubject().getLdapName()).certRequestId(Long.parseLong(requestId)).build();

            // 3. 创建编码器和上下文
            MessageEncoder<CmpMessage> encoder = PollReqMessageCodec.createEncoder(cmpConfig);
            SingleCertReqContext context = new SingleCertReqContext(pollReqMessage);

            // 4. 编码和发送请求
            encoder.encode(context, pollReqMessage);
            PKIMessages reqPkiMessages = context.getPKIMessages();
            byte[] reqData = reqPkiMessages.getEncoded();

            // 5. 发送HTTP请求
            String targetUrl = configManager.buildCmpRequestUrl();
            // 创建临时上下文用于查询操作
            SingleCertReqContext queryContext = new SingleCertReqContext(pollReqMessage);
            CmpRespResult cmpRespResult = sendCmpRequestAndParseResponse(targetUrl, reqData, cmpConfig, queryContext);

            // 6. 适配响应结果
            NetcaBpmsResponse response = responseAdapter.adaptCertificateStatusResponse(cmpRespResult);

            log.info("长江CA证书状态查询完成，请求ID: {}, 状态: {}", requestId, response.getStatus());
            return response;

        } catch (Exception e) {
            log.error("长江CA证书状态查询过程中发生未知错误", e);
            throw new CJCAException("CJCA_QUERY_ERROR", "证书状态查询失败: " + e.getMessage(), e);
        }
    }

    /**
     * 实现证书下载
     *
     * @param requestId 请求ID
     * @param systemId 系统ID
     * @return 业务平台响应
     * @throws Exception 下载过程中的异常
     */
    @Override
    protected NetcaBpmsResponse doDownloadCertificate(String requestId, String systemId) throws Exception {
        log.info("开始长江CA证书下载，请求ID: {}, 系统ID: {}", requestId, systemId);

        try {
            // 1. 构建CMP配置管理对象
            CmpMessageConfigManagement cmpConfig = configManager.createCmpMessageConfigManagement();

            // 2. 构建轮询请求消息（用于获取证书）
            PollReqMessage pollReqMessage = PollReqMessage.defaultMessage().toBuilder().senderStr(cmpConfig.getThirdPartyServerCommCert().getSubject().getLdapName()).recipientStr(cmpConfig.getCommunicationCert().getSubject().getLdapName()).certRequestId(Long.parseLong(requestId)).build();

            // 3. 创建编码器和上下文
            MessageEncoder<CmpMessage> encoder = PollReqMessageCodec.createEncoder(cmpConfig);
            SingleCertReqContext context = new SingleCertReqContext(pollReqMessage);

            // 4. 编码和发送请求
            encoder.encode(context, pollReqMessage);
            PKIMessages reqPkiMessages = context.getPKIMessages();
            byte[] reqData = reqPkiMessages.getEncoded();

            // 5. 发送HTTP请求
            String targetUrl = configManager.buildCmpRequestUrl();
            // 创建临时上下文用于下载操作
            SingleCertReqContext downloadContext = new SingleCertReqContext(pollReqMessage);
            CmpRespResult cmpRespResult = sendCmpRequestAndParseResponse(targetUrl, reqData, cmpConfig, downloadContext);

            // 6. 适配响应结果
            NetcaBpmsResponse response = responseAdapter.adaptCertificateDownloadResponse(cmpRespResult);

            log.info("长江CA证书下载完成，请求ID: {}, 状态: {}", requestId, response.getStatus());
            return response;

        } catch (Exception e) {
            log.error("长江CA证书下载过程中发生未知错误", e);
            throw new CJCAException("CJCA_DOWNLOAD_ERROR", "证书下载失败: " + e.getMessage(), e);
        }
    }

    /**
     * 实现证书注销
     *
     * @param businessCertAttribute 证书属性
     * @param configProject 项目配置
     * @param authorityOperator 授权操作员
     * @return 业务平台响应
     * @throws Exception 注销过程中的异常
     */
    @Override
    protected NetcaBpmsResponse doRevokeCertificate(BusinessCertAttribute businessCertAttribute, ConfigProject configProject, AuthorityOperator authorityOperator) throws Exception {
        log.info("开始长江CA证书注销，证书ID: {}, 项目ID: {}", businessCertAttribute.getId(), configProject.getId());

        try {
            // 1. 构建CMP配置管理对象
            CmpMessageConfigManagement cmpConfig = configManager.createCmpMessageConfigManagement();

            // 2. 解析注销原因 - 使用默认原因
            RevokeReasonEnum revokeReasonEnum = RevokeReasonEnum.UNSPECIFIED;

            // 3. 构建注销请求消息
            RevocationReqMessage revocationReqMessage = RevocationReqMessage.defaultMessage().toBuilder().senderStr(cmpConfig.getThirdPartyServerCommCert().getSubject().getLdapName()).recipientStr(cmpConfig.getCommunicationCert().getSubject().getLdapName()).certSn(businessCertAttribute.getCertSn()).issuer("CN=CJCA") // 使用默认颁发者DN
                    .reason(revokeReasonEnum.getCode()).build();

            // 4. 创建编码器和上下文
            MessageEncoder<CmpMessage> encoder = RevocationReqMessageCodec.createEncoder(cmpConfig);
            SingleCertReqContext context = new SingleCertReqContext(revocationReqMessage);

            // 5. 编码和发送请求
            encoder.encode(context, revocationReqMessage);
            PKIMessages reqPkiMessages = context.getPKIMessages();
            byte[] reqData = reqPkiMessages.getEncoded();

            // 6. 发送HTTP请求
            String targetUrl = configManager.buildCmpRequestUrl();
            // 创建临时上下文用于注销操作
            SingleCertReqContext revokeContext = new SingleCertReqContext(revocationReqMessage);
            CmpRespResult cmpRespResult = sendCmpRequestAndParseResponse(targetUrl, reqData, cmpConfig, revokeContext);

            // 7. 适配响应结果
            NetcaBpmsResponse response = responseAdapter.adaptCertificateRevocationResponse(cmpRespResult);

            log.info("长江CA证书注销完成，证书ID: {}, 状态: {}", businessCertAttribute.getId(), response.getStatus());
            return response;

        } catch (Exception e) {
            log.error("长江CA证书注销过程中发生未知错误", e);
            throw new CJCAException("CJCA_REVOKE_ERROR", "证书注销失败: " + e.getMessage(), e);
        }
    }

    /**
     * 管理员PIN解密实现
     *
     * 长江CA平台不支持管理员PIN解密功能，此方法将抛出UnsupportedOperationException异常。
     * 长江CA平台使用不同的密钥保护机制，不依赖传统的管理员PIN解密功能。
     *
     * @param encryptedPin 加密的PIN
     * @param systemId 系统ID
     * @return 解密后的PIN
     * @throws UnsupportedOperationException 长江CA不支持此功能
     */
    @Override
    protected String doDecryptAdministratorPin(String encryptedPin, String systemId) {
        log.warn("长江CA平台不支持管理员PIN解密功能，系统ID: {}", systemId);
        throw new UnsupportedOperationException("长江CA平台不支持管理员PIN解密功能。长江CA使用不同的密钥保护机制，不依赖传统的管理员PIN解密。");
    }

    // ==================== 业务信息保存方法（临时实现） ====================

    /**
     * 保存业务信息
     *
     * 注意：这是临时实现，完整的业务信息保存逻辑将在后续任务中实现
     *
     * @param userId 用户ID
     * @param projectId 项目ID
     * @param certType 证书类型
     * @param p10 证书请求
     * @param linkman 联系人信息
     * @param signKeyPair 签名密钥对
     * @param userPin 用户PIN
     * @param adminPin 管理员PIN
     * @param bpmsReqId 业务平台请求ID
     * @return 保存结果
     * @throws Exception 保存过程中的异常
     */
    @Override
    protected Map<String, Object> saveBusinessInfoBy(Integer userId, String projectId, int certType, String p10, Linkman linkman, KeyPairWrapper signKeyPair, String userPin, String adminPin, String bpmsReqId) throws Exception {
        log.info("保存长江CA业务信息，用户ID: {}, 项目ID: {}, 请求ID: {}", userId, projectId, bpmsReqId);

        try {
            // 1. 验证输入参数
            if (userId == null || !StringUtils.hasText(projectId) || !StringUtils.hasText(bpmsReqId)) {
                throw new IllegalArgumentException("保存业务信息的必要参数不能为空");
            }

            // 2. 构建业务信息记录
            Map<String, Object> businessInfo = new java.util.HashMap<>();
            businessInfo.put("userId", userId);
            businessInfo.put("projectId", projectId);
            businessInfo.put("certType", certType);
            businessInfo.put("p10", p10);
            businessInfo.put("bpmsReqId", bpmsReqId);
            businessInfo.put("userPin", userPin);
            businessInfo.put("adminPin", adminPin);
            businessInfo.put("createTime", new Date());
            businessInfo.put("platform", "CHANGJIANG_CA");

            // 3. 添加联系人信息
            if (linkman != null) {
                businessInfo.put("linkmanName", linkman.getName());
                businessInfo.put("linkmanPhone", linkman.getPhone());
                businessInfo.put("linkmanEmail", linkman.getEmail());
            }

            // 4. 添加密钥对信息（如果存在）
            if (signKeyPair != null) {
                businessInfo.put("hasKeyPair", true);
                businessInfo.put("keyAlgorithm", "RSA"); // 默认算法
            } else {
                businessInfo.put("hasKeyPair", false);
            }

            log.info("长江CA业务信息保存成功，用户ID: {}, 项目ID: {}, 请求ID: {}", userId, projectId, bpmsReqId);
            return businessInfo;

        } catch (Exception e) {
            log.error("保存长江CA业务信息失败", e);
            throw new CJCAException("CJCA_SAVE_BUSINESS_INFO_ERROR", "保存业务信息失败: " + e.getMessage(), e);
        }
    }

    // ==================== updateBusinessInfo 抽象策略方法实现 ====================

    @Override
    protected Map<String, Object> parseResponseData(NetcaBpmsResponse bpmsResponse) throws Exception {
        log.debug("解析长江CA响应数据，状态: {}", bpmsResponse.getStatus());

        Map<String, Object> responseData = new HashMap<>();
        responseData.put("reqId", bpmsResponse.getReqId());
        responseData.put("status", bpmsResponse.getStatus());
        responseData.put("rawResponse", bpmsResponse);

        // 长江CA特定的响应解析逻辑
        // 注意：这里需要根据实际的长江CA响应格式进行解析
        // 目前使用简化的解析逻辑
        if (bpmsResponse.getResponseResult() != null) {
            responseData.put("responseResult", bpmsResponse.getResponseResult());
        }

        return responseData;
    }

    @Override
    protected BusinessResponseStatus mapResponseStatus(NetcaBpmsResponse bpmsResponse) {
        Long status = bpmsResponse.getStatus();

        // 长江CA特定的状态映射逻辑
        if (status != null) {
            if (status == 0L) {
                return BusinessResponseStatus.SUCCESS;
            } else if (status == 1L) {
                return BusinessResponseStatus.PENDING;
            } else {
                return BusinessResponseStatus.FAILURE;
            }
        }

        return BusinessResponseStatus.FAILURE;
    }

    @Override
    protected Map<String, Map<String, Object>> extractCertificateInfo(Map<String, Object> responseData) throws Exception {
        // 长江CA特定的证书信息提取逻辑
        Map<String, Map<String, Object>> certInfo = new HashMap<>();

        // 注意：这里需要根据实际的长江CA响应格式进行证书信息提取
        // 目前使用简化的提取逻辑
        @SuppressWarnings("unchecked")
        NetcaBpmsResponse rawResponse = (NetcaBpmsResponse) responseData.get("rawResponse");

        if (rawResponse != null && rawResponse.getResponseResult() != null) {
            // 模拟证书信息提取（实际实现需要根据长江CA的响应格式）
            Map<String, Object> signCertMessage = new HashMap<>();
            signCertMessage.put("certSn", "CJCA_SIGN_" + System.currentTimeMillis());
            signCertMessage.put("certValidityStart", new Date());
            signCertMessage.put("certValidityEnd", new Date(System.currentTimeMillis() + 365L * 24 * 60 * 60 * 1000));

            certInfo.put("signCertMessage", signCertMessage);
        }

        return certInfo;
    }

    @Override
    protected Map<String, Object> handleSuccessResponse(Map<String, Object> responseData,
                                                        int businessCertId, int userId) throws Exception {
        log.debug("处理长江CA成功响应，业务证书ID: {}, 用户ID: {}", businessCertId, userId);

        Map<String, Object> result = new HashMap<>();
        result.put("processed", true);
        result.put("platform", "CHANGJIANG_CA");
        result.put("businessCertId", businessCertId);
        result.put("userId", userId);

        // 长江CA特定的成功处理逻辑
        // 可以在这里添加特定的业务逻辑，如通知、日志记录等

        return result;
    }

    @Override
    protected Map<String, Object> handlePendingResponse(Map<String, Object> responseData,
                                                        BusinessUser businessUser) throws Exception {
        log.debug("处理长江CA待审核响应，用户ID: {}", businessUser.getId());

        Map<String, Object> result = new HashMap<>();
        result.put("processed", true);
        result.put("platform", "CHANGJIANG_CA");
        result.put("userStatus", "APPLYING");
        result.put("userId", businessUser.getId());

        // 长江CA特定的待审核处理逻辑
        // 可以在这里添加特定的业务逻辑，如发送通知、更新状态等

        return result;
    }

    @Override
    protected Map<String, Object> handleFailureResponse(Map<String, Object> responseData,
                                                        NetcaBpmsResponse bpmsResponse) throws Exception {
        log.debug("处理长江CA失败响应，状态码: {}", bpmsResponse.getStatus());

        Map<String, Object> result = new HashMap<>();
        result.put("processed", true);
        result.put("platform", "CHANGJIANG_CA");
        result.put("errorCode", bpmsResponse.getStatus());
        result.put("errorMessage", bpmsResponse.toString());

        // 长江CA特定的失败处理逻辑
        // 可以在这里添加特定的错误处理逻辑，如错误分类、重试机制等

        return result;
    }

    @Override
    protected String formatErrorMessage(NetcaBpmsResponse bpmsResponse, String defaultMessage) {
        // 长江CA特定的错误信息格式化
        Long status = bpmsResponse.getStatus();
        String reqId = bpmsResponse.getReqId();

        StringBuilder errorMessage = new StringBuilder();
        errorMessage.append("长江CA错误");

        if (status != null) {
            errorMessage.append(" [状态码: ").append(status).append("]");
        }

        if (StringUtils.hasText(reqId)) {
            errorMessage.append(" [请求ID: ").append(reqId).append("]");
        }

        errorMessage.append(" - ").append(defaultMessage);

        return errorMessage.toString();
    }

    /**
     * 生成证书请求ID
     */
    private long generateCertificateRequestId() {
        return IdUtil.getSnowflakeNextId();
    }

    /**
     * 为CMP构建用户信息
     */
    private UserInfo buildUserInfoForCmp(BusinessUser businessUser) {
        // 使用builder模式创建UserInfo
        return UserInfo.builder()
                .name(businessUser.getName())
                .identityType(businessUser.getIdentityType())
                .identity(businessUser.getIdentity())
                .telephone(businessUser.getPhone())
                .address(businessUser.getOfficialResidence())
                .build();
    }




    /**
     * 发送CMP请求并解析响应
     * 参考 RegisterCert.java 中的实现
     */
    private CmpRespResult sendCmpRequestAndParseResponse(String url,
                                                         byte[] requestData,
                                                         CmpMessageConfigManagement cmpConfig,
                                                         SingleCertReqContext context) throws Exception {
        log.debug("发送CMP请求到URL: {}, 请求数据大小: {} bytes", url, requestData.length);

        // 1. 创建HTTP客户端
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .build();

        // 2. 构建HTTP请求 - 使用正确的媒体类型
        MediaType mediaType = MediaType.get("application/pkixcmp");
        RequestBody requestBody = RequestBody.create(mediaType, requestData);
        okhttp3.Request request = new okhttp3.Request.Builder().url(url).post(requestBody).build();

        // 3. 发送请求并获取响应
        try (okhttp3.Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new CJCAException("CJCA_HTTP_ERROR", String.format("HTTP请求失败: %d %s", response.code(), response.message()));
            }

            if (response.body() == null) {
                throw new CJCAException("CJCA_RESPONSE_ERROR", "响应体为空");
            }

            byte[] responseData = response.body().bytes();
            log.debug("收到响应数据，大小: {} bytes", responseData.length);

            // 4. 解析CMP响应 - 参考 RegisterCert.java 的实现
            PKIMessages respPkiMessages = PKIMessages.getInstance(ASN1Sequence.getInstance(responseData));

            // 5. 创建解码器并解码响应
            MessageDecoder<PKIMessage> decoder = P10CertReqMessageCodec.createDecoder(cmpConfig);
            decoder.decode(context, respPkiMessages.toPKIMessageArray()[0]);

            // 6. 获取解码结果
            CmpRespResult cmpRespResult = context.getCmpRespResult();

            if (cmpRespResult == null) {
                throw new CJCAException("CJCA_DECODE_ERROR", "解码CMP响应失败，结果为空");
            }

            log.debug("CMP响应解析成功，签名证书: {}, 加密证书: {}", cmpRespResult.getSignCertPem() != null ? "存在" : "不存在", cmpRespResult.getEncCertPem() != null ? "存在" : "不存在");

            return cmpRespResult;

        } catch (Exception e) {
            log.error("发送CMP请求或解析响应时发生错误", e);
            if (e instanceof CJCAException) {
                throw e;
            }
            throw new CJCAException("CJCA_REQUEST_ERROR", "CMP请求处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析注销原因
     */
    private RevokeReasonEnum parseRevokeReason(String revokeReason) {
        if (!StringUtils.hasText(revokeReason)) {
            return RevokeReasonEnum.UNSPECIFIED;
        }

        switch (revokeReason.toUpperCase()) {
            case "KEY_COMPROMISE":
                return RevokeReasonEnum.KEY_COMPROMISE;
            case "CA_COMPROMISE":
                return RevokeReasonEnum.CA_COMPROMISE;
            case "AFFILIATION_CHANGED":
                return RevokeReasonEnum.AFFILIATION_CHANGED;
            case "SUPERSEDED":
                return RevokeReasonEnum.SUPERSEDED;
            case "CESSATION_OF_OPERATION":
                return RevokeReasonEnum.CESSATION_OF_OPERATION;
            case "CERTIFICATE_HOLD":
                return RevokeReasonEnum.CERTIFICATE_HOLD;
            case "PRIVILEGE_WITHDRAWN":
                return RevokeReasonEnum.PRIVILEGE_WITHDRAWN;
            default:
                return RevokeReasonEnum.UNSPECIFIED;
        }
    }


}
