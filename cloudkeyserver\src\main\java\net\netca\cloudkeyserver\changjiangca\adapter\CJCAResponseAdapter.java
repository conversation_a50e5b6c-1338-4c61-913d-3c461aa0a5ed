package net.netca.cloudkeyserver.changjiangca.adapter;

import lombok.extern.slf4j.Slf4j;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.pojo.resp.NetcaBpmsResponse;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.pojo.CertInfo;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.vo.ResponseResult;
import net.netca.cloudkeyserver.changjiangca.exception.CJCAException;
import net.netca.sdk.entity.CmpRespResult;
import net.netca.sdk.constants.PKIStatusEnum;
import net.netca.sdk.constants.PKIFailureInfoEnum;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Base64;
import java.util.ArrayList;
import java.util.List;

/**
 * 长江CA响应适配器
 *
 * <p>负责将长江CA平台的CMP响应结果适配为CloudKey系统的标准响应格式。</p>
 *
 * <h3>主要功能：</h3>
 * <ul>
 *   <li>PKI状态码转换和映射</li>
 *   <li>证书数据格式转换（DER/PEM to Base64）</li>
 *   <li>错误信息详细解析和映射</li>
 *   <li>响应结果标准化处理</li>
 * </ul>
 *
 * <h3>支持的操作类型：</h3>
 * <ul>
 *   <li>证书申请响应适配</li>
 *   <li>证书状态查询响应适配</li>
 *   <li>证书下载响应适配</li>
 *   <li>证书注销响应适配</li>
 * </ul>
 *
 * <AUTHOR> Team
 * @since 2.42.1
 * @see CmpRespResult
 * @see PKIStatusEnum
 */
@Component
@Slf4j
public class CJCAResponseAdapter {

    // ==================== 公共响应适配方法 ====================

    /**
     * 适配证书申请响应
     *
     * <p>将CMP证书申请响应转换为业务平台标准响应格式。</p>
     *
     * @param cmpRespResult CMP响应结果，不能为null
     * @return 业务平台响应对象
     * @throws CJCAException 当适配过程中发生错误时抛出异常
     * @throws IllegalArgumentException 当输入参数为null时抛出异常
     */
    public NetcaBpmsResponse adaptCertificateApplicationResponse(CmpRespResult cmpRespResult) throws CJCAException {
        validateInput(cmpRespResult, "证书申请响应");
        log.debug("开始适配证书申请响应，请求ID: {}", cmpRespResult.getRespCertRequestId());

        try {
            NetcaBpmsResponse response = createBaseResponse(cmpRespResult);

            // 适配状态信息
            adaptStatusInfo(cmpRespResult, response);

            // 适配证书数据（如果申请成功且有证书数据）
            if (isSuccessStatus(cmpRespResult)) {
                adaptCertificateData(cmpRespResult, response);
                adaptEncryptionKeyData(cmpRespResult, response);
            }

            log.debug("证书申请响应适配完成，状态: {}, 请求ID: {}",
                    response.getStatus(), cmpRespResult.getRespCertRequestId());
            return response;

        } catch (Exception e) {
            log.error("适配证书申请响应失败，请求ID: {}", cmpRespResult.getRespCertRequestId(), e);
            throw new CJCAException("CJCA_ADAPT_ERROR",
                    "适配证书申请响应失败: " + e.getMessage(), e);
        }
    }

    /**
     * 适配证书状态查询响应
     *
     * <p>将CMP证书状态查询响应转换为业务平台标准响应格式。</p>
     *
     * @param cmpRespResult CMP响应结果，不能为null
     * @return 业务平台响应对象
     * @throws CJCAException 当适配过程中发生错误时抛出异常
     * @throws IllegalArgumentException 当输入参数为null时抛出异常
     */
    public NetcaBpmsResponse adaptCertificateStatusResponse(CmpRespResult cmpRespResult) throws CJCAException {
        validateInput(cmpRespResult, "证书状态查询响应");
        log.debug("开始适配证书状态查询响应，请求ID: {}", cmpRespResult.getRespCertRequestId());

        try {
            NetcaBpmsResponse response = createBaseResponse(cmpRespResult);

            // 适配状态信息
            adaptStatusInfo(cmpRespResult, response);

            // 适配证书数据（如果状态为成功且有证书数据）
            if (isSuccessStatus(cmpRespResult)) {
                adaptCertificateData(cmpRespResult, response);
            }

            log.debug("证书状态查询响应适配完成，状态: {}, 请求ID: {}",
                    response.getStatus(), cmpRespResult.getRespCertRequestId());
            return response;

        } catch (Exception e) {
            log.error("适配证书状态查询响应失败，请求ID: {}", cmpRespResult.getRespCertRequestId(), e);
            throw new CJCAException("CJCA_ADAPT_ERROR",
                    "适配证书状态查询响应失败: " + e.getMessage(), e);
        }
    }

    /**
     * 适配证书下载响应
     *
     * <p>将CMP证书下载响应转换为业务平台标准响应格式。</p>
     * <p>证书下载操作要求成功时必须包含证书数据。</p>
     *
     * @param cmpRespResult CMP响应结果，不能为null
     * @return 业务平台响应对象
     * @throws CJCAException 当适配过程中发生错误时抛出异常
     * @throws IllegalArgumentException 当输入参数为null时抛出异常
     */
    public NetcaBpmsResponse adaptCertificateDownloadResponse(CmpRespResult cmpRespResult) throws CJCAException {
        validateInput(cmpRespResult, "证书下载响应");
        log.debug("开始适配证书下载响应，请求ID: {}", cmpRespResult.getRespCertRequestId());

        try {
            NetcaBpmsResponse response = createBaseResponse(cmpRespResult);

            // 适配状态信息
            adaptStatusInfo(cmpRespResult, response);

            // 证书下载必须包含证书数据
            if (isSuccessStatus(cmpRespResult)) {
                if (hasCertificateData(cmpRespResult)) {
                    adaptCertificateData(cmpRespResult, response);
                    log.debug("证书下载成功，包含证书数据");
                } else {
                    log.warn("证书下载成功但未返回证书数据，请求ID: {}", cmpRespResult.getRespCertRequestId());
                    overrideResponseStatus(response, "FAILED", "证书下载成功但未返回证书数据");
                }
            }

            log.debug("证书下载响应适配完成，状态: {}, 请求ID: {}",
                    response.getStatus(), cmpRespResult.getRespCertRequestId());
            return response;

        } catch (Exception e) {
            log.error("适配证书下载响应失败，请求ID: {}", cmpRespResult.getRespCertRequestId(), e);
            throw new CJCAException("CJCA_ADAPT_ERROR",
                    "适配证书下载响应失败: " + e.getMessage(), e);
        }
    }

    /**
     * 适配证书注销响应
     *
     * <p>将CMP证书注销响应转换为业务平台标准响应格式。</p>
     * <p>证书注销操作通常只需要状态信息，不需要证书数据。</p>
     *
     * @param cmpRespResult CMP响应结果，不能为null
     * @return 业务平台响应对象
     * @throws CJCAException 当适配过程中发生错误时抛出异常
     * @throws IllegalArgumentException 当输入参数为null时抛出异常
     */
    public NetcaBpmsResponse adaptCertificateRevocationResponse(CmpRespResult cmpRespResult) throws CJCAException {
        validateInput(cmpRespResult, "证书注销响应");
        log.debug("开始适配证书注销响应，请求ID: {}", cmpRespResult.getRespCertRequestId());

        try {
            NetcaBpmsResponse response = createBaseResponse(cmpRespResult);

            // 适配状态信息
            adaptStatusInfo(cmpRespResult, response);

            // 记录注销的证书序列号（如果有）
            if (StringUtils.hasText(cmpRespResult.getRevokedSigCertSn())) {
                log.debug("证书注销成功，注销证书序列号: {}", cmpRespResult.getRevokedSigCertSn());
            }

            log.debug("证书注销响应适配完成，状态: {}, 请求ID: {}",
                    response.getStatus(), cmpRespResult.getRespCertRequestId());
            return response;

        } catch (Exception e) {
            log.error("适配证书注销响应失败，请求ID: {}", cmpRespResult.getRespCertRequestId(), e);
            throw new CJCAException("CJCA_ADAPT_ERROR",
                    "适配证书注销响应失败: " + e.getMessage(), e);
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 验证输入参数
     *
     * @param cmpRespResult CMP响应结果
     * @param operationType 操作类型描述
     * @throws IllegalArgumentException 当输入参数为null时抛出异常
     */
    private void validateInput(CmpRespResult cmpRespResult, String operationType) {
        if (cmpRespResult == null) {
            throw new IllegalArgumentException("CMP响应结果不能为null，操作类型: " + operationType);
        }
    }

    /**
     * 创建基础响应对象
     *
     * @param cmpRespResult CMP响应结果
     * @return 基础响应对象
     */
    private NetcaBpmsResponse createBaseResponse(CmpRespResult cmpRespResult) {
        NetcaBpmsResponse response = new NetcaBpmsResponse();

        // 设置请求ID
        if (cmpRespResult.getRespCertRequestId() != 0) {
            response.setReqId(String.valueOf(cmpRespResult.getRespCertRequestId()));
        }

        return response;
    }

    /**
     * 适配状态信息
     *
     * <p>将PKI状态枚举转换为业务平台标准状态。</p>
     *
     * @param cmpRespResult CMP响应结果
     * @param response 业务平台响应
     */
    private void adaptStatusInfo(CmpRespResult cmpRespResult, NetcaBpmsResponse response) {
        PKIStatusEnum pkiStatus = cmpRespResult.getPkiStatusEnum();

        if (pkiStatus == null) {
            setResponseStatus(response, "UNKNOWN", "未知的PKI状态");
            return;
        }

        switch (pkiStatus) {
            case ACCEPTED:
                setResponseStatus(response, "SUCCESS", "操作成功");
                break;
            case GRANTED_WITH_MODS:
                setResponseStatus(response, "SUCCESS", "操作成功（有修改）");
                break;
            case REJECTION:
                setResponseStatus(response, "FAILED", "操作被拒绝: " + buildFailureInfo(cmpRespResult));
                break;
            case WAITING:
                setResponseStatus(response, "PENDING", "操作等待中");
                break;
            case REVOCATION_WARNING:
                setResponseStatus(response, "WARNING", "注销警告");
                break;
            case REVOCATION_NOTIFICATION:
                setResponseStatus(response, "SUCCESS", "注销通知");
                break;
            case KEY_UPDATE_WARNING:
                setResponseStatus(response, "WARNING", "密钥更新警告");
                break;
            default:
                setResponseStatus(response, "UNKNOWN", "未知状态: " + pkiStatus.getDescription());
                break;
        }
    }

    /**
     * 设置响应状态和消息
     *
     * @param response 响应对象
     * @param status 状态
     * @param message 消息
     */
    private void setResponseStatus(NetcaBpmsResponse response, String status, String message) {
        // 设置状态码
        try {
            response.setStatus(Long.valueOf(getStatusCode(status)));
        } catch (Exception e) {
            log.warn("设置状态码失败，使用默认值: {}", status, e);
            response.setStatus(999L); // 默认未知状态码
        }

        // 设置响应结果对象
        ResponseResult result = new ResponseResult();
        result.setStatus(Integer.valueOf(getStatusCode(status)));
        result.setMsg(message);
        response.setResponseResult(result);
    }

    /**
     * 覆盖响应状态（用于特殊情况）
     *
     * @param response 响应对象
     * @param status 新状态
     * @param message 新消息
     */
    private void overrideResponseStatus(NetcaBpmsResponse response, String status, String message) {
        setResponseStatus(response, status, message);
    }

    /**
     * 获取状态码
     *
     * @param status 状态字符串
     * @return 状态码
     */
    private String getStatusCode(String status) {
        switch (status) {
            case "SUCCESS":
                return "200";
            case "FAILED":
                return "500";
            case "PENDING":
                return "202";
            case "WARNING":
                return "300";
            default:
                return "999";
        }
    }

    /**
     * 判断是否为成功状态
     *
     * @param cmpRespResult CMP响应结果
     * @return 是否为成功状态
     */
    private boolean isSuccessStatus(CmpRespResult cmpRespResult) {
        PKIStatusEnum pkiStatus = cmpRespResult.getPkiStatusEnum();
        return pkiStatus == PKIStatusEnum.ACCEPTED;
    }

    /**
     * 检查是否有证书数据
     *
     * @param cmpRespResult CMP响应结果
     * @return 是否有证书数据
     */
    private boolean hasCertificateData(CmpRespResult cmpRespResult) {
        return StringUtils.hasText(cmpRespResult.getSignCertPem()) ||
                (cmpRespResult.getSignCertDer() != null && cmpRespResult.getSignCertDer().length > 0);
    }

    /**
     * 适配证书数据
     *
     * <p>将CMP响应中的证书数据转换为NetcaBpmsResponse的CertInfo数组格式。</p>
     *
     * @param cmpRespResult CMP响应结果
     * @param response 业务平台响应
     */
    private void adaptCertificateData(CmpRespResult cmpRespResult, NetcaBpmsResponse response) {
        List<CertInfo> certInfoList = new ArrayList<>();

        // 处理签名证书
        String signCertData = cmpRespResult.getSignCertPem();
        if (signCertData != null) {
            CertInfo signCertInfo = new CertInfo();
            signCertInfo.setCertUsage(2L); // 2表示签名证书
            signCertInfo.setCertContent(signCertData);
            certInfoList.add(signCertInfo);
            log.debug("设置签名证书数据成功");
        }

        // 处理加密证书
        String encCertData = cmpRespResult.getEncCertPem();
        if (encCertData != null) {
            CertInfo encCertInfo = new CertInfo();
            encCertInfo.setCertUsage(1L); // 1表示加密证书
            encCertInfo.setCertContent(encCertData);

            // 设置加密密钥对（如果有）
            if (cmpRespResult.getEncPrivateKey() != null && cmpRespResult.getEncPrivateKey().length > 0) {
                String encKeyData = Base64.getEncoder().encodeToString(cmpRespResult.getEncPrivateKey());
                encCertInfo.setEnckeyPair(encKeyData);
                log.debug("设置加密密钥对数据成功，长度: {}", cmpRespResult.getEncPrivateKey().length);
            }

            certInfoList.add(encCertInfo);
            log.debug("设置加密证书数据成功");
        }

        // 设置证书信息数组
        if (!certInfoList.isEmpty()) {
            response.setCertInfo(certInfoList.toArray(new CertInfo[0]));
            log.debug("设置证书信息数组成功，包含 {} 个证书", certInfoList.size());
        }
    }

    /**
     * 适配加密密钥数据
     *
     * <p>此方法已合并到 adaptCertificateData 中，保留用于向后兼容。</p>
     *
     * @param cmpRespResult CMP响应结果
     * @param response 业务平台响应
     */
    private void adaptEncryptionKeyData(CmpRespResult cmpRespResult, NetcaBpmsResponse response) {
        // 此方法的功能已合并到 adaptCertificateData 中
        // 保留此方法以避免破坏现有调用
        log.debug("加密密钥数据适配已合并到证书数据适配中");
    }

    /**
     * 提取证书数据
     *
     * @param pemData PEM格式证书数据
     * @param derData DER格式证书数据
     * @return Base64编码的证书数据
     */
    private String extractCertificateData(String pemData, byte[] derData) {
        // 优先使用PEM格式
        if (StringUtils.hasText(pemData)) {
            String base64Data = convertPemToBase64(pemData);
            if (base64Data != null) {
                log.debug("使用PEM格式证书数据");
                return base64Data;
            }
        }

        // 如果PEM不可用，使用DER格式
        if (derData != null && derData.length > 0) {
            log.debug("使用DER格式证书数据，长度: {}", derData.length);
            return Base64.getEncoder().encodeToString(derData);
        }

        return null;
    }

    /**
     * 构建失败信息
     *
     * @param cmpRespResult CMP响应结果
     * @return 失败信息描述
     */
    private String buildFailureInfo(CmpRespResult cmpRespResult) {
        StringBuilder failureInfo = new StringBuilder();

        // 添加失败原因
        if (StringUtils.hasText(cmpRespResult.getFailureReason())) {
            failureInfo.append(cmpRespResult.getFailureReason());
        }

        // 添加失败代码描述
        if (cmpRespResult.getFailureCode() != null) {
            String codeDescription = getFailureCodeDescription(cmpRespResult.getFailureCode());
            if (failureInfo.length() > 0) {
                failureInfo.append(" (").append(codeDescription).append(")");
            } else {
                failureInfo.append(codeDescription);
            }
        }

        // 如果没有具体信息，返回通用描述
        if (failureInfo.length() == 0) {
            failureInfo.append("请检查证书申请参数和CA平台状态");
        }

        return failureInfo.toString();
    }

    /**
     * 获取失败代码描述
     *
     * @param failureCode 失败代码
     * @return 失败代码描述
     */
    private String getFailureCodeDescription(Integer failureCode) {
        try {
            PKIFailureInfoEnum failureEnum = PKIFailureInfoEnum.toPKIFailureInfo(failureCode);
            return failureEnum.getDescription();
        } catch (Exception e) {
            log.warn("无法解析失败代码: {}", failureCode, e);
            return "未知错误代码: " + failureCode;
        }
    }

    /**
     * 将PEM格式转换为Base64
     *
     * @param pemData PEM格式数据
     * @return Base64编码数据
     */
    private String convertPemToBase64(String pemData) {
        if (!StringUtils.hasText(pemData)) {
            return null;
        }

        // 移除PEM头尾标记和换行符，提取Base64内容
        String base64Content = pemData
                .replaceAll("-----BEGIN [^-]+-----", "")
                .replaceAll("-----END [^-]+-----", "")
                .replaceAll("\\s+", "");

        return base64Content;
    }
}
