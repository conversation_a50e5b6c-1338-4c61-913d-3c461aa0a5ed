package net.netca.cloudkeyserver.changjiangca.exception;

import lombok.Getter;

/**
 * 长江CA CMP协议相关异常类
 * 
 * 用于处理长江CA平台CMP协议相关的异常情况，包括：
 * - CMP消息编码失败
 * - CMP消息解码失败
 * - CMP协议版本不兼容
 * - CMP消息格式错误
 * - CMP响应状态错误
 * - 证书请求处理失败
 * - 证书下载失败
 * - 证书注销失败
 * 
 * <AUTHOR> Team
 * @since 2.42.1
 */
@Getter
public class CJCACmpProtocolException extends CJCAException {
    
    private static final long serialVersionUID = 1L;
    
    /** CMP消息类型
     * -- GETTER --
     *  获取CMP消息类型
     *
     * @return CMP消息类型
     */
    private final String messageType;
    
    /** CMP状态码
     * -- GETTER --
     *  获取CMP状态码
     *
     * @return CMP状态码
     */
    private final Integer cmpStatusCode;
    
    /** CMP失败信息
     * -- GETTER --
     *  获取CMP失败信息
     *
     * @return CMP失败信息
     */
    private final String cmpFailInfo;
    
    /**
     * 构造方法
     * 
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     */
    public CJCACmpProtocolException(String errorCode, String errorMessage) {
        super(errorCode, errorMessage);
        this.messageType = null;
        this.cmpStatusCode = null;
        this.cmpFailInfo = null;
    }
    
    /**
     * 构造方法（带原因异常）
     * 
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @param cause 原因异常
     */
    public CJCACmpProtocolException(String errorCode, String errorMessage, Throwable cause) {
        super(errorCode, errorMessage, cause);
        this.messageType = null;
        this.cmpStatusCode = null;
        this.cmpFailInfo = null;
    }
    
    /**
     * 构造方法（带CMP详细信息）
     * 
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @param messageType CMP消息类型
     * @param cmpStatusCode CMP状态码
     * @param cmpFailInfo CMP失败信息
     */
    public CJCACmpProtocolException(String errorCode, String errorMessage,
                                    String messageType, Integer cmpStatusCode, String cmpFailInfo) {
        super(errorCode, errorMessage);
        this.messageType = messageType;
        this.cmpStatusCode = cmpStatusCode;
        this.cmpFailInfo = cmpFailInfo;
    }
    
    /**
     * 构造方法（仅错误消息）
     * 
     * @param errorMessage 错误消息
     */
    public CJCACmpProtocolException(String errorMessage) {
        super("CJCA_CMP_PROTOCOL_ERROR", errorMessage);
        this.messageType = null;
        this.cmpStatusCode = null;
        this.cmpFailInfo = null;
    }
    
    /**
     * 构造方法（仅错误消息和原因异常）
     * 
     * @param errorMessage 错误消息
     * @param cause 原因异常
     */
    public CJCACmpProtocolException(String errorMessage, Throwable cause) {
        super("CJCA_CMP_PROTOCOL_ERROR", errorMessage, cause);
        this.messageType = null;
        this.cmpStatusCode = null;
        this.cmpFailInfo = null;
    }

    /**
     * 创建CMP消息编码失败异常
     * 
     * @param messageType 消息类型
     * @param cause 原因异常
     * @return CjcaCmpProtocolException实例
     */
    public static CJCACmpProtocolException encodeError(String messageType, Throwable cause) {
        return new CJCACmpProtocolException("CJCA_CMP_ENCODE_ERROR",
            "CMP消息编码失败，消息类型: " + messageType, cause);
    }
    
    /**
     * 创建CMP消息解码失败异常
     * 
     * @param cause 原因异常
     * @return CjcaCmpProtocolException实例
     */
    public static CJCACmpProtocolException decodeError(Throwable cause) {
        return new CJCACmpProtocolException("CJCA_CMP_DECODE_ERROR",
            "CMP响应消息解码失败", cause);
    }
    
    /**
     * 创建CMP协议版本不兼容异常
     * 
     * @param supportedVersion 支持的版本
     * @param actualVersion 实际版本
     * @return CjcaCmpProtocolException实例
     */
    public static CJCACmpProtocolException versionMismatch(String supportedVersion, String actualVersion) {
        return new CJCACmpProtocolException("CJCA_CMP_VERSION_MISMATCH",
            "CMP协议版本不兼容，支持版本: " + supportedVersion + "，实际版本: " + actualVersion);
    }
    
    /**
     * 创建CMP响应状态错误异常
     * 
     * @param statusCode CMP状态码
     * @param failInfo 失败信息
     * @return CjcaCmpProtocolException实例
     */
    public static CJCACmpProtocolException responseStatusError(int statusCode, String failInfo) {
        return new CJCACmpProtocolException("CJCA_CMP_RESPONSE_STATUS_ERROR",
            "CMP响应状态错误", null, statusCode, failInfo);
    }
    
    /**
     * 创建证书申请失败异常
     * 
     * @param requestId 请求ID
     * @param failInfo 失败信息
     * @return CjcaCmpProtocolException实例
     */
    public static CJCACmpProtocolException certificateRequestFailed(String requestId, String failInfo) {
        return new CJCACmpProtocolException("CJCA_CERT_REQUEST_FAILED",
            "证书申请失败，请求ID: " + requestId + "，失败信息: " + failInfo);
    }
    
    /**
     * 创建证书下载失败异常
     * 
     * @param requestId 请求ID
     * @param reason 失败原因
     * @return CjcaCmpProtocolException实例
     */
    public static CJCACmpProtocolException certificateDownloadFailed(String requestId, String reason) {
        return new CJCACmpProtocolException("CJCA_CERT_DOWNLOAD_FAILED",
            "证书下载失败，请求ID: " + requestId + "，原因: " + reason);
    }
    
    /**
     * 创建证书注销失败异常
     * 
     * @param serialNumber 证书序列号
     * @param reason 失败原因
     * @return CjcaCmpProtocolException实例
     */
    public static CJCACmpProtocolException certificateRevocationFailed(String serialNumber, String reason) {
        return new CJCACmpProtocolException("CJCA_CERT_REVOCATION_FAILED",
            "证书注销失败，证书序列号: " + serialNumber + "，原因: " + reason);
    }
    
    /**
     * 创建CMP消息格式错误异常
     * 
     * @param messageType 消息类型
     * @param reason 错误原因
     * @return CjcaCmpProtocolException实例
     */
    public static CJCACmpProtocolException messageFormatError(String messageType, String reason) {
        return new CJCACmpProtocolException("CJCA_CMP_MESSAGE_FORMAT_ERROR",
            "CMP消息格式错误，消息类型: " + messageType + "，原因: " + reason);
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder(super.toString());
        if (messageType != null) {
            sb.append(" - 消息类型: ").append(messageType);
        }
        if (cmpStatusCode != null) {
            sb.append(" - CMP状态码: ").append(cmpStatusCode);
        }
        if (cmpFailInfo != null && !cmpFailInfo.isEmpty()) {
            sb.append(" - CMP失败信息: ").append(cmpFailInfo);
        }
        return sb.toString();
    }
}
