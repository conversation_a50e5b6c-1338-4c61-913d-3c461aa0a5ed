package net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.pojo.resp;

import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.pojo.*;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.vo.ResponseResult;

import java.util.Map;

/**
 * 【证书服务请求响应类】
 * <AUTHOR>
 *
 */
public class NetcaBpmsResponse {
	//业务单号
	private String reqId;
	private Long status;
	private Long kmType;
	
	/**
	 * 响应结果
	 * 参考 @ResponseResult
	 */
	private ResponseResult responseResult;
	
	/**
	 * 证书信息
	 * 参考 @CertInfo
	 */
	private CertInfo[] certInfo;
	
	
	/*****增加如下   获取业务信息接口   所使用的属性参数*/
	private User user;
	private User organization;
	private Linkman linkman;
	private Map moreInfo;
	
	private RequestFile requestFile;

	private DeleteCert[] deleteCert;
	
	public String getReqId() {
		return reqId;
	}

	public void setReqId(String reqId) {
		this.reqId = reqId;
	}
	
	public Long getStatus() {
		return status;
	}

	public void setStatus(Long status) {
		this.status = status;
	}
	
	public ResponseResult getResponseResult() {
		return responseResult;
	}

	public void setResponseResult(ResponseResult responseResult) {
		this.responseResult = responseResult;
	}

	public CertInfo[] getCertInfo() {
		return certInfo;
	}

	public void setCertInfo(CertInfo[] certInfo) {
		this.certInfo = certInfo;
	}

	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}

	public User getOrganization() {
		return organization;
	}

	public void setOrganization(User organization) {
		this.organization = organization;
	}

	public Linkman getLinkman() {
		return linkman;
	}

	public void setLinkman(Linkman linkman) {
		this.linkman = linkman;
	}
	
	public Map getMoreInfo() {
		return moreInfo;
	}
	
	public void setMoreInfo(Map moreInfo) {
		this.moreInfo = moreInfo;
	}
	
	public RequestFile getRequestFile() {
		return requestFile;
	}

	public void setRequestFile(RequestFile requestFile) {
		this.requestFile = requestFile;
	}

	public DeleteCert[] getDeleteCert() {
		return deleteCert;
	}

	public void setDeleteCert(DeleteCert[] deleteCert) {
		this.deleteCert = deleteCert;
	}

	public Long getKmType() {
		return kmType;
	}

	public void setKmType(Long kmType) {
		this.kmType = kmType;
	}
}
