package net.netca.cloudkeyserver.changjiangca.exception;

import lombok.Getter;

/**
 * 长江CA连接相关异常类
 * 
 * 用于处理长江CA平台连接相关的异常情况，包括：
 * - 网络连接失败
 * - HTTP请求错误
 * - SSL证书验证失败
 * - 连接超时
 * - 读取超时
 * - 服务器响应错误
 * 
 * <AUTHOR> Team
 * @since 2.42.1
 */
@Getter
public class CJCAConnectionException extends CJCAException {
    
    private static final long serialVersionUID = 1L;
    
    /** HTTP状态码
     * -- GETTER --
     *  获取HTTP状态码
     *
     * @return HTTP状态码，如果没有则返回null
     */
    private final Integer httpStatusCode;
    
    /** 响应内容
     * -- GETTER --
     *  获取响应内容
     *
     * @return 响应内容，如果没有则返回null
     */
    private final String responseContent;
    
    /**
     * 构造方法
     * 
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     */
    public CJCAConnectionException(String errorCode, String errorMessage) {
        super(errorCode, errorMessage);
        this.httpStatusCode = null;
        this.responseContent = null;
    }
    
    /**
     * 构造方法（带原因异常）
     * 
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @param cause 原因异常
     */
    public CJCAConnectionException(String errorCode, String errorMessage, Throwable cause) {
        super(errorCode, errorMessage, cause);
        this.httpStatusCode = null;
        this.responseContent = null;
    }
    
    /**
     * 构造方法（带HTTP状态码）
     * 
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @param httpStatusCode HTTP状态码
     * @param responseContent 响应内容
     */
    public CJCAConnectionException(String errorCode, String errorMessage,
                                   Integer httpStatusCode, String responseContent) {
        super(errorCode, errorMessage);
        this.httpStatusCode = httpStatusCode;
        this.responseContent = responseContent;
    }
    
    /**
     * 构造方法（仅错误消息）
     * 
     * @param errorMessage 错误消息
     */
    public CJCAConnectionException(String errorMessage) {
        super("CJCA_CONNECTION_ERROR", errorMessage);
        this.httpStatusCode = null;
        this.responseContent = null;
    }
    
    /**
     * 构造方法（仅错误消息和原因异常）
     * 
     * @param errorMessage 错误消息
     * @param cause 原因异常
     */
    public CJCAConnectionException(String errorMessage, Throwable cause) {
        super("CJCA_CONNECTION_ERROR", errorMessage, cause);
        this.httpStatusCode = null;
        this.responseContent = null;
    }

    /**
     * 创建网络连接失败异常
     * 
     * @param url 连接URL
     * @param cause 原因异常
     * @return CjcaConnectionException实例
     */
    public static CJCAConnectionException networkError(String url, Throwable cause) {
        return new CJCAConnectionException("CJCA_NETWORK_ERROR",
            "无法连接到长江CA平台: " + url, cause);
    }
    
    /**
     * 创建HTTP请求错误异常
     * 
     * @param httpStatusCode HTTP状态码
     * @param responseContent 响应内容
     * @return CjcaConnectionException实例
     */
    public static CJCAConnectionException httpError(int httpStatusCode, String responseContent) {
        return new CJCAConnectionException("CJCA_HTTP_ERROR",
            "HTTP请求失败，状态码: " + httpStatusCode, httpStatusCode, responseContent);
    }
    
    /**
     * 创建连接超时异常
     * 
     * @param timeout 超时时间（毫秒）
     * @return CjcaConnectionException实例
     */
    public static CJCAConnectionException connectionTimeout(int timeout) {
        return new CJCAConnectionException("CJCA_CONNECTION_TIMEOUT",
            "连接长江CA平台超时，超时时间: " + timeout + "ms");
    }
    
    /**
     * 创建读取超时异常
     * 
     * @param timeout 超时时间（毫秒）
     * @return CjcaConnectionException实例
     */
    public static CJCAConnectionException readTimeout(int timeout) {
        return new CJCAConnectionException("CJCA_READ_TIMEOUT",
            "读取长江CA平台响应超时，超时时间: " + timeout + "ms");
    }
    
    /**
     * 创建SSL证书验证失败异常
     * 
     * @param reason 失败原因
     * @param cause 原因异常
     * @return CjcaConnectionException实例
     */
    public static CJCAConnectionException sslError(String reason, Throwable cause) {
        return new CJCAConnectionException("CJCA_SSL_ERROR",
            "SSL证书验证失败: " + reason, cause);
    }
    
    /**
     * 创建响应为空异常
     * 
     * @return CjcaConnectionException实例
     */
    public static CJCAConnectionException emptyResponse() {
        return new CJCAConnectionException("CJCA_EMPTY_RESPONSE",
            "长江CA平台返回空响应");
    }
    
    /**
     * 创建响应格式错误异常
     * 
     * @param expectedFormat 期望格式
     * @param actualContent 实际内容
     * @return CjcaConnectionException实例
     */
    public static CJCAConnectionException invalidResponseFormat(String expectedFormat, String actualContent) {
        return new CJCAConnectionException("CJCA_INVALID_RESPONSE_FORMAT",
            "响应格式错误，期望: " + expectedFormat + "，实际: " + 
            (actualContent != null && actualContent.length() > 100 ? 
             actualContent.substring(0, 100) + "..." : actualContent));
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder(super.toString());
        if (httpStatusCode != null) {
            sb.append(" - HTTP状态码: ").append(httpStatusCode);
        }
        if (responseContent != null && !responseContent.isEmpty()) {
            sb.append(" - 响应内容: ").append(
                responseContent.length() > 200 ? 
                responseContent.substring(0, 200) + "..." : responseContent);
        }
        return sb.toString();
    }
}
