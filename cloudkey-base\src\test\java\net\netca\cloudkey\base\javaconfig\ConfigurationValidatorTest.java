package net.netca.cloudkey.base.javaconfig;

import net.netca.cloudkey.base.exception.ConfigurationException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.env.Environment;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 配置验证器测试类
 *
 * <AUTHOR>
 */
public class ConfigurationValidatorTest {

    @Mock
    private Environment environment;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 初始化配置读取器
        ConfigurationReader.initialize(environment);
        
        // 设置系统属性用于测试
        System.setProperty("test.boolean.true", "true");
        System.setProperty("test.boolean.false", "false");
        System.setProperty("test.boolean.invalid", "not-a-boolean");
        System.setProperty("test.integer", "123");
        System.setProperty("test.integer.invalid", "not-an-integer");
        System.setProperty("test.string", "test-value");
    }

    @Test
    public void testBooleanValidationRule() {
        // 创建布尔验证规则
        ConfigurationValidator.BooleanValidationRule rule = new ConfigurationValidator.BooleanValidationRule(true);
        
        // 测试有效值
        assertDoesNotThrow(() -> rule.validate("test.key", "true"));
        assertDoesNotThrow(() -> rule.validate("test.key", "false"));
        assertDoesNotThrow(() -> rule.validate("test.key", "TRUE"));
        assertDoesNotThrow(() -> rule.validate("test.key", "FALSE"));
        
        // 测试无效值
        assertThrows(ConfigurationException.class, () -> rule.validate("test.key", "yes"));
        assertThrows(ConfigurationException.class, () -> rule.validate("test.key", "no"));
        assertThrows(ConfigurationException.class, () -> rule.validate("test.key", "1"));
        assertThrows(ConfigurationException.class, () -> rule.validate("test.key", "0"));
        
        // 测试默认值
        assertEquals("true", rule.getDefaultValue());
    }

    @Test
    public void testIntegerValidationRule() {
        // 创建整数验证规则
        ConfigurationValidator.IntegerValidationRule rule = new ConfigurationValidator.IntegerValidationRule(100, 1, 1000);
        
        // 测试有效值
        assertDoesNotThrow(() -> rule.validate("test.key", "1"));
        assertDoesNotThrow(() -> rule.validate("test.key", "500"));
        assertDoesNotThrow(() -> rule.validate("test.key", "1000"));
        
        // 测试无效值
        assertThrows(ConfigurationException.class, () -> rule.validate("test.key", "0"));
        assertThrows(ConfigurationException.class, () -> rule.validate("test.key", "1001"));
        assertThrows(ConfigurationException.class, () -> rule.validate("test.key", "not-an-integer"));
        
        // 测试默认值
        assertEquals("100", rule.getDefaultValue());
    }

    @Test
    public void testStringValidationRule() {
        // 创建字符串验证规则
        ConfigurationValidator.StringValidationRule rule = new ConfigurationValidator.StringValidationRule(
                "default", "^[a-z0-9-]+$", "Value must contain only lowercase letters, numbers, and hyphens", true);
        
        // 测试有效值
        assertDoesNotThrow(() -> rule.validate("test.key", "test-value"));
        assertDoesNotThrow(() -> rule.validate("test.key", "123"));
        assertDoesNotThrow(() -> rule.validate("test.key", "test-123"));
        
        // 测试无效值
        assertThrows(ConfigurationException.class, () -> rule.validate("test.key", "TEST-VALUE"));
        assertThrows(ConfigurationException.class, () -> rule.validate("test.key", "test_value"));
        assertThrows(ConfigurationException.class, () -> rule.validate("test.key", ""));
        assertThrows(ConfigurationException.class, () -> rule.validate("test.key", null));
        
        // 测试默认值
        assertEquals("default", rule.getDefaultValue());
    }

    @Test
    public void testRegisterAndValidate() {
        // 注册验证规则
        ConfigurationValidator.registerRule(
                "test.boolean.true", 
                new ConfigurationValidator.BooleanValidationRule(false)
        );
        
        ConfigurationValidator.registerRule(
                "test.integer", 
                new ConfigurationValidator.IntegerValidationRule(0, 0, 1000)
        );
        
        ConfigurationValidator.registerRule(
                "test.string", 
                new ConfigurationValidator.StringValidationRule("default", "^[a-z0-9-]+$", "Invalid format")
        );
        
        // 验证注册的规则
        assertDoesNotThrow(() -> ConfigurationValidator.validate("test.boolean.true", "true"));
        assertDoesNotThrow(() -> ConfigurationValidator.validate("test.integer", "123"));
        assertDoesNotThrow(() -> ConfigurationValidator.validate("test.string", "test-value"));
        
        // 验证未注册的键
        assertDoesNotThrow(() -> ConfigurationValidator.validate("test.unregistered", "any-value"));
    }

    @Test
    public void testValidateAll() {
        // 注册验证规则
        ConfigurationValidator.registerRule(
                "test.boolean.true", 
                new ConfigurationValidator.BooleanValidationRule(false)
        );
        
        ConfigurationValidator.registerRule(
                "test.integer", 
                new ConfigurationValidator.IntegerValidationRule(0, 0, 1000)
        );
        
        ConfigurationValidator.registerRule(
                "test.string", 
                new ConfigurationValidator.StringValidationRule("default", "^[a-z0-9-]+$", "Invalid format")
        );
        
        // 验证所有配置
        assertDoesNotThrow(() -> ConfigurationValidator.validateAll());
        
        // 注册一个会失败的规则
        ConfigurationValidator.registerRule(
                "test.boolean.invalid", 
                new ConfigurationValidator.BooleanValidationRule(false)
        );
        
        // 验证所有配置应该失败
        assertThrows(ConfigurationException.class, () -> ConfigurationValidator.validateAll());
    }
}