package net.netca.cloudkey.base.service;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Preconditions;
import net.netca.cloudkey.base.constant.CertUserTypeEnum;
import net.netca.cloudkey.base.constant.EnablePinRuleEnum;
import net.netca.cloudkey.base.constant.StatusConstant;
import net.netca.cloudkey.base.constant.WebResultEnum;
import net.netca.cloudkey.base.dto.common.OptionItem;
import net.netca.cloudkey.base.dto.project.ProjectSearchDTO;
import net.netca.cloudkey.base.exception.CloudKeyRuntimeException;
import net.netca.cloudkey.base.mapper.ConfigPinRuleMapper;
import net.netca.cloudkey.base.mapper.ConfigProjectMapper;
import net.netca.cloudkey.base.po.BusinessCert;
import net.netca.cloudkey.base.po.ConfigPinRule;
import net.netca.cloudkey.base.po.ConfigProject;
import net.netca.cloudkey.base.util.CustomStringUtils;
import net.netca.cloudkey.base.util.DateUtil;
import net.netca.common.notice.constants.SupportSmsTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service("configProjectService")
public class ConfigProjectService {
    @Autowired
    private ConfigProjectMapper configProjectMapper;

    @Autowired
    private BusinessCertService businessCertService;

    @Autowired
    private ConfigPinRuleMapper configPinRuleMapper;

    public String insert(ConfigProject configProject) {

        this.checkSomeFields(configProject);

        Date now = DateUtil.getNow();
        configProject.setGmtCreate(now);
        configProject.setGmtModified(now);
        configProjectMapper.insert(configProject);
        return configProject.getId();
    }

    public ConfigProject selectById(String id) {
        return configProjectMapper.selectById(id);
    }

    /**
     * 根据id查询项目配置的登录相关的配置
     *
     * @param id
     * @return
     */
    public ConfigProject selectLoginTypeById(String id) {
        QueryWrapper<ConfigProject> wrapper = new QueryWrapper<>();
        wrapper.select("user_fingerprint_register_login_type, user_login_type, login_rule_id");
        wrapper.eq("id", id);
        List<ConfigProject> configProjects = configProjectMapper.selectList(wrapper);
        if (configProjects == null || configProjects.isEmpty()) {
            throw new CloudKeyRuntimeException(String.format("id=%s 的项目不存在", id));
        }
        return configProjects.get(0);
    }

    /**
     * 根据证书ID查询该证书所支持的用户身份验证方式
     *
     * @param certId
     * @return
     */
    public int selectLoginTypeValueByCertId(Integer certId) {
        ConfigProject project = selectByCertId(certId);
        return Integer.parseInt(project.getUserLoginType());
    }

    public ConfigProject selectByCertId(Integer certId) {
        BusinessCert businessCert = businessCertService.selectByIdWithoutP10(certId);
        ConfigProject project = selectById(businessCert.getProjectId());
        return project;
    }

    public ConfigProject selectProjectUidByCertId(Integer certId) {
        BusinessCert businessCert = businessCertService.selectProjectById(certId);
        if (Objects.isNull(businessCert)) {
            return new ConfigProject();
        }
        return new ConfigProject().selectOne(new QueryWrapper<ConfigProject>().select("id, project_uid").eq("id", businessCert.getProjectId()));
    }

    public List<ConfigProject> selectByProjectUid(String projectUid) {
        QueryWrapper<ConfigProject> wrapper = new QueryWrapper();
        wrapper.eq("project_uid", projectUid);
        return new ConfigProject().selectList(wrapper);

    }

    public int delProj(ConfigProject configProject) {

        configProject.setStatus(StatusConstant.FREEZE.getCode());
        configProject.setGmtModified(DateUtil.getNow());

        return configProjectMapper.updateById(configProject);
    }

    public int updateProj(ConfigProject project) {

        this.checkSomeFields(project);

        project.setGmtModified(DateUtil.getNow());
        return configProjectMapper.updateById(project);
    }

    public List<OptionItem> selectOption() {
        return configProjectMapper.selectOption();
    }

    /**
     * 根据院区id获取机构员工项目
     * @param districtId 院区id 可以为空，如果是空，则查询所有机构员工项目
     */
    public List<OptionItem> selectOrgUserOption(Integer districtId) {
        LambdaQueryWrapper<ConfigProject> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ConfigProject::getId, ConfigProject::getName)
                .eq(ConfigProject::getStatus, StatusConstant.NORMAL.getCode())
                .eq(ConfigProject::getCertUserType, CertUserTypeEnum.EMPLOYEE.getCode())
                .eq(Objects.nonNull(districtId), ConfigProject::getDistrictId, districtId);
        List<ConfigProject> configProjects = configProjectMapper.selectList(wrapper);
        return configProjects.stream().
                map(item -> new OptionItem(item.getName(), item.getId())).collect(Collectors.toList());
    }

    public List<OptionItem> selectOptionByDistrict(List<Integer> districtIds) {
        return configProjectMapper.selectOptionByDistrict(districtIds);
    }

    public int selectCountByProjectUid(String projectUid) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("project_uid", projectUid);
        wrapper.eq("status", StatusConstant.NORMAL.getCode());
        return Math.toIntExact(configProjectMapper.selectCount(wrapper));
    }

    public int updateProjectByUid(String oldProjectUid, String newProjectUid) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("project_uid", oldProjectUid);
        wrapper.eq("status", StatusConstant.NORMAL.getCode());

        ConfigProject configProject = new ConfigProject();
        configProject.setProjectUid(newProjectUid);
        configProject.setGmtModified(DateUtil.getNow());

        return configProjectMapper.update(configProject, wrapper);
    }

    /**
     * 字段约束检查
     *
     * @param configProject
     */
    private void checkSomeFields(ConfigProject configProject) {

        final String FIELD_NAME = "name";
        final String FIELD_ID = "id";
        final String FIELD_STATUS = "status";

        QueryWrapper<ConfigProject> wrapper = new QueryWrapper<>();
        wrapper.eq(FIELD_NAME, configProject.getName()).eq(FIELD_STATUS, StatusConstant.NORMAL)
                .ne(!CustomStringUtils.isBlank(configProject.getId()),
                        FIELD_ID, configProject.getId());

        Preconditions.checkArgument(configProject.selectCount(wrapper) == 0,
                "已存在同名项目");


        if (EnablePinRuleEnum.ENABLE.getCode().equals(configProject.getEnablePinRule())) {
            Integer pinRuleId = configProject.getPinRuleId();
            int count = Math.toIntExact(configPinRuleMapper.selectCount(new QueryWrapper<ConfigPinRule>().eq("id", pinRuleId)));
            Preconditions.checkArgument(count == 1, String.format("id=%s的口令规则不存在", pinRuleId));
        }

        // 检查短信平台类型(允许为空，但如果不为空 则必须要在 SupportSmsTypeEnum 之内)
        String smsPlatform = configProject.getSmsPlatform();
        if (StringUtils.isNotBlank(smsPlatform)) {
            SupportSmsTypeEnum smsType = SupportSmsTypeEnum.getByName(smsPlatform);
            // 防御性代码：阻止通过非常规途径设置的控制台类型
            if (SupportSmsTypeEnum.CONSOLE.equals(smsType)) {
                throw new CloudKeyRuntimeException(WebResultEnum.PARAM_ERROR.getCode(), String.format("非法参数 smsPlatform[%s]", smsPlatform));
            }
            Preconditions.checkNotNull(smsType, String.format("未知的短信平台：%s", smsPlatform));
        }

    }

    public int selectCountByProjectInfo(ProjectSearchDTO projectSearchDTO) {
        return configProjectMapper.selectCountByProjectInfo(projectSearchDTO);
    }

    public List<ConfigProject> selectPageByProjectInfo(ProjectSearchDTO projectSearchDTO, Integer pageIndex, Integer pageSize) {
        return configProjectMapper.selectPageByProjectInfo(projectSearchDTO, pageIndex, pageSize);
    }

    /**
     * 主键ID列表查询项目信息
     *
     * @param projectIds
     * @return
     */
    public List<ConfigProject> selectByProjectByIds(List<String> projectIds) {
        if (projectIds == null || projectIds.isEmpty()) {
            return Collections.emptyList();
        }
        QueryWrapper<ConfigProject> wrapper = new QueryWrapper<>();
        wrapper.in("id", projectIds);
        return configProjectMapper.selectList(wrapper);
    }

    public List<OptionItem> selectProjectUidOption() {

        return configProjectMapper.selectProjectUidOption();
    }
}
