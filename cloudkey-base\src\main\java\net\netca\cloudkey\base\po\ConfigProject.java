package net.netca.cloudkey.base.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2019-02-13
 */
@TableName("config_project")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConfigProject extends Model<ConfigProject> {

    private static final long serialVersionUID = 1L;

    /**
     * 系统项目ID，按规则随机产生
     */
    @TableId(value = "id",type = IdType.INPUT)
    private String id;
    private String name;
    /**
     * 用户验证登录类型（常量）
     */
    @TableField("user_login_type")
    private String userLoginType;


    // zhuanji说明，需求一直在变，最初是只有绑定指纹的，现在改成绑定设备等都使用这个字段。
    @TableField("user_fingerprint_register_login_type")
    private String userFingerprintRegisterLoginType;
    @TableField("bpms_system_id")
    private String bpmsSystemId;
    /**
     * 业务平台配置的模板ID
     */
    @TableField("bpms_project_id")
    private String bpmsProjectId;
    @TableField("bpms_business_center_id")
    private Integer bpmsBusinessCenterId;
    /**
     * 业务平台-证书模板ID
     */
    @TableField("bpms_cert_template_id")
    private String bpmsCertTemplateId;

    /**
     * 项目标识
     */
    @TableField("project_uid")
    private String projectUid;

    private Integer status;
    /**
     * 证书用户类型
     */
    @TableField("cert_user_type")
    private Integer certUserType;
    /**
     * 证书算法类型
     */
    @TableField("cert_algo_type")
    private Integer certAlgoType;

    /**
     * 允许自动签发，0不允许，1允许
     */
    @TableField("allow_auto_approve")
    private Integer allowAutoApprove;

    /**
     * 证书有效期间隔
     */
    @TableField("cert_interval")
    private Integer certInterval;

    /**
     * 过期前多久可续期证书，单位：天
     */
    @TableField(value = "renewal_cert_interval")
    private Integer renewalCertInterval;

    /**
     * 证书有效期间隔单位，1表示天，2表示月
     */
    @TableField("cert_interval_unit")
    private Integer certIntervalUnit;

    /**
     * 签章图片是否自动审核，0不允许，1允许 2 不存在图片时允许（包括已审核）
     */
    private Integer allowAutoReviewSealPic;

    /**
     * 管理员id
     */
    @TableField("operator_id")
    private Integer operatorId;
    @TableField("operator_modify_id")
    private Integer operatorModifyId;
    @TableField("login_rule_id")
    private Integer loginRuleId;

    @TableField("config_linkman_id")
    private Integer configLinkmanId;

    @TableField("allow_user_make_cert_count")
    private Integer allowUserMakeCertCount;

    /**
     * 被授权人检验等级 0 不检验 1检验是否为相同职业
     */
    private Integer authorizeCheckLevel;

    /**
     * 授权的最大个数 0表示不限制
     */
    private Integer maxAuthorizeCount;

    /**
     * 默认的授权有效期长度，单位为天，0表示不限制
     */
    @TableField("default_authorize_interval")
    private Integer defaultAuthorizeInterval;

    /**
     * 默认的授权次数，0表示不限制
     */
    @TableField("default_authorize_count")
    private Integer defaultAuthorizeCount;

    @TableField("department_id")
    private Integer departmentId;

    @TableField("approve_level")
    private Integer approveLevel;

    @TableField("limit_bind_device")
    private Integer limitBindDevice;

    @TableField("sms_auth_mode")
    private Integer smsAuthMode;

    @TableField("face_type")
    private String faceType;//项目支持的人脸识别模式

    @TableField("send_pic_url")
    private String sendPicUrl;

    @TableField("auth_check_url")
    private String authCheckUrl;

    @TableField("user_push_url")
    private String userPushUrl;

    /**
     * 是否开启强密码校验
     */
    @TableField("enable_pin_rule")
    private Integer enablePinRule;

    /**
     * 强密码规则id
     */
    @TableField("pin_rule_id")
    private Integer pinRuleId;

    private String memo;

    @TableField("gmt_create")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    @TableField("gmt_modified")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

    /**
     * 短信平台，如果没有配置，则发送短信时使用阿里云平台短信进行发送
     * @since V2.18
     */
    @TableField("sms_platform")
    private String smsPlatform;

    /**
     * 业务消息推送地址(方便在系统上进行配置，和不同项目可以配置不同的推送地址)
     * @since V2.18
     */
    @TableField("business_info_notify_url")
    private String businessInfoNotifyUrl;

    /**
     * 业务消息推送加密证书
     * @since V2.18
     */
    @TableField("business_info_notify_cert")
    private String businessInfoNotifyCert;

    /**
     * 是否允许接口上传签章图片，0不允许，1允许
     * @since V2.22
     */
    @TableField("allow_api_upload_seal_pic")
    private Integer allowApiUploadSealPic;

    /**
     * 是否为事件证书 0 不是 1 是
     * @since V2.23
     */
    @TableField("event_cert_flag")
    private Integer eventCertFlag;

    /**
     * 职业编号
     * @since V2.24
     */
    @TableField("occupation_id")
    private Integer occupationId;

    /**
     * 院区ID
     */
    @TableField("district_id")
    private Integer districtId;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
