package net.netca.cloudkey.base.javaconfig;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.core.env.Environment;
import org.springframework.mock.env.MockEnvironment;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 优化的 ShardingSphere 配置测试
 * 测试数据源配置的独立存在性
 *
 * <AUTHOR>
 */
public class OptimizedShardingSphereConfigurationTest {

    private MockEnvironment environment;

    @BeforeEach
    void setUp() {
        environment = new MockEnvironment();
        ConfigurationReader.initialize(environment);
    }

    @Test
    void testShardingSphereDataSourceConfigIndependence() {
        // 设置 ShardingSphere 数据源配置
        environment.setProperty("shardingsphere.datasource.business.url", "************************************");
        environment.setProperty("shardingsphere.datasource.business.username", "business_user");
        environment.setProperty("shardingsphere.datasource.business.password", "business_pass");
        environment.setProperty("shardingsphere.datasource.archive.url", "***********************************");
        environment.setProperty("shardingsphere.datasource.archive.username", "archive_user");
        environment.setProperty("shardingsphere.datasource.archive.password", "archive_pass");
        
        // 不设置 spring.datasource 配置，测试独立存在性
        environment.setProperty("cloudkey.shardingsphere.enabled", "true");

        // 重新初始化配置读取器
        ConfigurationReader.initialize(environment);

        // 验证 ShardingSphere 数据源配置存在
        assertTrue(ConfigurationReader.hasShardingSphereDataSourceConfig(), 
                "ShardingSphere datasource configuration should exist independently");

        // 验证标准数据源配置不存在
        assertFalse(ConfigurationReader.hasStandardDataSourceConfig(), 
                "Standard datasource configuration should not exist");

        // 检查配置独立性
        ConfigurationReader.DataSourceConfigStatus status = ConfigurationReader.checkDataSourceConfigIndependence();
        assertTrue(status.hasShardingSphereConfig, "Should have ShardingSphere config");
        assertFalse(status.hasStandardConfig, "Should not have standard config");
        assertTrue(status.shardingSphereEnabled, "ShardingSphere should be enabled");
        assertTrue(status.configurationIndependent, "Configuration should be independent");
    }

    @Test
    void testStandardDataSourceConfigIndependence() {
        // 设置标准数据源配置
        environment.setProperty("spring.datasource.url", "************************************");
        environment.setProperty("spring.datasource.username", "standard_user");
        environment.setProperty("spring.datasource.password", "standard_pass");
        environment.setProperty("spring.datasource.driver-class-name", "com.mysql.cj.jdbc.Driver");
        
        // 不设置 shardingsphere.datasource 配置，测试独立存在性
        environment.setProperty("cloudkey.shardingsphere.enabled", "false");

        // 重新初始化配置读取器
        ConfigurationReader.initialize(environment);

        // 验证标准数据源配置存在
        assertTrue(ConfigurationReader.hasStandardDataSourceConfig(), 
                "Standard datasource configuration should exist independently");

        // 验证 ShardingSphere 数据源配置不存在
        assertFalse(ConfigurationReader.hasShardingSphereDataSourceConfig(), 
                "ShardingSphere datasource configuration should not exist");

        // 检查配置独立性
        ConfigurationReader.DataSourceConfigStatus status = ConfigurationReader.checkDataSourceConfigIndependence();
        assertFalse(status.hasShardingSphereConfig, "Should not have ShardingSphere config");
        assertTrue(status.hasStandardConfig, "Should have standard config");
        assertFalse(status.shardingSphereEnabled, "ShardingSphere should be disabled");
        assertTrue(status.configurationIndependent, "Configuration should be independent");
    }

    @Test
    void testBothDataSourceConfigsCanCoexist() {
        // 设置两种数据源配置同时存在
        environment.setProperty("shardingsphere.datasource.business.url", "************************************");
        environment.setProperty("shardingsphere.datasource.business.username", "business_user");
        environment.setProperty("shardingsphere.datasource.business.password", "business_pass");
        
        environment.setProperty("spring.datasource.url", "************************************");
        environment.setProperty("spring.datasource.username", "standard_user");
        environment.setProperty("spring.datasource.password", "standard_pass");
        
        environment.setProperty("cloudkey.shardingsphere.enabled", "true");

        // 重新初始化配置读取器
        ConfigurationReader.initialize(environment);

        // 验证两种配置都存在
        assertTrue(ConfigurationReader.hasShardingSphereDataSourceConfig(), 
                "ShardingSphere datasource configuration should exist");
        assertTrue(ConfigurationReader.hasStandardDataSourceConfig(), 
                "Standard datasource configuration should exist");

        // 检查配置独立性
        ConfigurationReader.DataSourceConfigStatus status = ConfigurationReader.checkDataSourceConfigIndependence();
        assertTrue(status.hasShardingSphereConfig, "Should have ShardingSphere config");
        assertTrue(status.hasStandardConfig, "Should have standard config");
        assertTrue(status.shardingSphereEnabled, "ShardingSphere should be enabled");
        assertTrue(status.configurationIndependent, "Configuration should be independent");
    }

    @Test
    void testConfigurationPrefixUsage() {
        // 测试配置前缀的正确使用
        environment.setProperty("shardingsphere.datasource.business.url", "*********************************************");
        environment.setProperty("shardingsphere.datasource.business.username", "sharding_user");
        
        environment.setProperty("spring.datasource.url", "*******************************************");
        environment.setProperty("spring.datasource.username", "spring_user");

        // 重新初始化配置读取器
        ConfigurationReader.initialize(environment);

        // 验证配置前缀的正确读取
        assertEquals("*********************************************", 
                ConfigurationReader.getString("shardingsphere.datasource.business.url", null),
                "Should read shardingsphere.datasource prefix correctly");
        
        assertEquals("*******************************************", 
                ConfigurationReader.getString("spring.datasource.url", null),
                "Should read spring.datasource prefix correctly");
        
        assertEquals("sharding_user", 
                ConfigurationReader.getString("shardingsphere.datasource.business.username", null),
                "Should read shardingsphere.datasource username correctly");
        
        assertEquals("spring_user", 
                ConfigurationReader.getString("spring.datasource.username", null),
                "Should read spring.datasource username correctly");
    }
}