package net.netca.cloudkeyserver.changjiangca.constant;

import lombok.Getter;

/**
 * 长江CA证书模板枚举
 * 
 * 定义长江CA平台支持的证书模板类型，包括个人证书、员工证书、组织证书等。
 * 每个模板包含模板ID、显示名称、描述信息和适用场景。
 * 
 * 证书模板用于：
 * - 证书申请时指定证书类型
 * - 配置不同类型证书的属性和扩展
 * - 业务系统中的证书分类管理
 * 
 * <AUTHOR> Team
 * @since 2.42.1
 */
@Getter
public enum CJCACertificateTemplateEnum {

    // ==================== 个人证书模板 ====================
    
    /**
     * 个人签名证书模板
     * 适用于个人用户的数字签名场景
     */
    PERSONAL_SIGN("PERSONAL_SIGN_TEMPLATE", "个人签名证书", 
        "用于个人用户进行数字签名的证书模板，支持文档签名、邮件签名等场景", 
        CertificateUsageType.SIGN, CertificateUserType.PERSONAL),

    /**
     * 个人加密证书模板
     * 适用于个人用户的数据加密场景
     */
    PERSONAL_ENCRYPT("PERSONAL_ENCRYPT_TEMPLATE", "个人加密证书", 
        "用于个人用户进行数据加密的证书模板，支持文件加密、通信加密等场景", 
        CertificateUsageType.ENCRYPT, CertificateUserType.PERSONAL),

    /**
     * 个人双证书模板
     * 同时包含签名和加密功能
     */
    PERSONAL_DUAL("PERSONAL_DUAL_TEMPLATE", "个人双证书", 
        "包含签名和加密功能的个人证书模板，适用于需要完整PKI功能的个人用户", 
        CertificateUsageType.DUAL, CertificateUserType.PERSONAL),

    // ==================== 员工证书模板 ====================
    
    /**
     * 员工签名证书模板
     * 适用于企业员工的数字签名场景
     */
    EMPLOYEE_SIGN("EMPLOYEE_SIGN_TEMPLATE", "员工签名证书", 
        "用于企业员工进行数字签名的证书模板，支持公文签名、合同签名等企业场景", 
        CertificateUsageType.SIGN, CertificateUserType.EMPLOYEE),

    /**
     * 员工加密证书模板
     * 适用于企业员工的数据加密场景
     */
    EMPLOYEE_ENCRYPT("EMPLOYEE_ENCRYPT_TEMPLATE", "员工加密证书", 
        "用于企业员工进行数据加密的证书模板，支持企业内部通信加密、文档保护等场景", 
        CertificateUsageType.ENCRYPT, CertificateUserType.EMPLOYEE),

    /**
     * 员工双证书模板
     * 同时包含签名和加密功能
     */
    EMPLOYEE_DUAL("EMPLOYEE_DUAL_TEMPLATE", "员工双证书", 
        "包含签名和加密功能的员工证书模板，适用于需要完整PKI功能的企业员工", 
        CertificateUsageType.DUAL, CertificateUserType.EMPLOYEE),

    // ==================== 组织证书模板 ====================
    
    /**
     * 组织签名证书模板
     * 适用于组织机构的数字签名场景
     */
    ORGANIZATION_SIGN("ORGANIZATION_SIGN_TEMPLATE", "组织签名证书", 
        "用于组织机构进行数字签名的证书模板，支持机构公文、对外合同等场景", 
        CertificateUsageType.SIGN, CertificateUserType.ORGANIZATION),

    /**
     * 组织加密证书模板
     * 适用于组织机构的数据加密场景
     */
    ORGANIZATION_ENCRYPT("ORGANIZATION_ENCRYPT_TEMPLATE", "组织加密证书", 
        "用于组织机构进行数据加密的证书模板，支持机构间通信加密、数据保护等场景", 
        CertificateUsageType.ENCRYPT, CertificateUserType.ORGANIZATION),

    /**
     * 组织双证书模板
     * 同时包含签名和加密功能
     */
    ORGANIZATION_DUAL("ORGANIZATION_DUAL_TEMPLATE", "组织双证书", 
        "包含签名和加密功能的组织证书模板，适用于需要完整PKI功能的组织机构", 
        CertificateUsageType.DUAL, CertificateUserType.ORGANIZATION),

    // ==================== 服务器证书模板 ====================
    
    /**
     * SSL服务器证书模板
     * 适用于HTTPS网站和服务器认证
     */
    SSL_SERVER("SSL_SERVER_TEMPLATE", "SSL服务器证书", 
        "用于HTTPS网站和服务器身份认证的证书模板，支持网站加密传输和服务器身份验证", 
        CertificateUsageType.SERVER_AUTH, CertificateUserType.SERVER),

    /**
     * 代码签名证书模板
     * 适用于软件代码签名
     */
    CODE_SIGNING("CODE_SIGNING_TEMPLATE", "代码签名证书", 
        "用于软件代码签名的证书模板，确保软件的完整性和来源可信", 
        CertificateUsageType.CODE_SIGN, CertificateUserType.ORGANIZATION);

    // ==================== 枚举属性 ====================
    
    /**
     * 模板ID - 长江CA平台中的模板标识
     */
    private final String templateId;
    
    /**
     * 显示名称 - 用于界面显示的友好名称
     */
    private final String displayName;
    
    /**
     * 描述信息 - 模板的详细说明
     */
    private final String description;
    
    /**
     * 证书用途类型
     */
    private final CertificateUsageType usageType;
    
    /**
     * 证书用户类型
     */
    private final CertificateUserType userType;

    /**
     * 构造方法
     * 
     * @param templateId 模板ID
     * @param displayName 显示名称
     * @param description 描述信息
     * @param usageType 证书用途类型
     * @param userType 证书用户类型
     */
    CJCACertificateTemplateEnum(String templateId, String displayName, String description,
                                CertificateUsageType usageType, CertificateUserType userType) {
        this.templateId = templateId;
        this.displayName = displayName;
        this.description = description;
        this.usageType = usageType;
        this.userType = userType;
    }

    // ==================== 工具方法 ====================
    
    /**
     * 根据模板ID查找证书模板
     * 
     * @param templateId 模板ID
     * @return 对应的证书模板枚举，如果未找到则返回null
     */
    public static CJCACertificateTemplateEnum findByTemplateId(String templateId) {
        if (templateId == null || templateId.trim().isEmpty()) {
            return null;
        }
        
        for (CJCACertificateTemplateEnum template : values()) {
            if (template.getTemplateId().equals(templateId.trim())) {
                return template;
            }
        }
        return null;
    }
    
    /**
     * 根据用户类型和用途类型查找证书模板
     * 
     * @param userType 用户类型
     * @param usageType 用途类型
     * @return 匹配的证书模板数组
     */
    public static CJCACertificateTemplateEnum[] findByUserTypeAndUsage(CertificateUserType userType,
                                                                       CertificateUsageType usageType) {
        return java.util.Arrays.stream(values())
                .filter(template -> template.getUserType() == userType && template.getUsageType() == usageType)
                .toArray(CJCACertificateTemplateEnum[]::new);
    }
    
    /**
     * 获取所有个人证书模板
     * 
     * @return 个人证书模板数组
     */
    public static CJCACertificateTemplateEnum[] getPersonalTemplates() {
        return findByUserType(CertificateUserType.PERSONAL);
    }
    
    /**
     * 获取所有员工证书模板
     * 
     * @return 员工证书模板数组
     */
    public static CJCACertificateTemplateEnum[] getEmployeeTemplates() {
        return findByUserType(CertificateUserType.EMPLOYEE);
    }
    
    /**
     * 获取所有组织证书模板
     * 
     * @return 组织证书模板数组
     */
    public static CJCACertificateTemplateEnum[] getOrganizationTemplates() {
        return findByUserType(CertificateUserType.ORGANIZATION);
    }
    
    /**
     * 根据用户类型查找证书模板
     * 
     * @param userType 用户类型
     * @return 匹配的证书模板数组
     */
    public static CJCACertificateTemplateEnum[] findByUserType(CertificateUserType userType) {
        return java.util.Arrays.stream(values())
                .filter(template -> template.getUserType() == userType)
                .toArray(CJCACertificateTemplateEnum[]::new);
    }
    
    /**
     * 检查模板是否支持签名功能
     * 
     * @return true表示支持签名，false表示不支持
     */
    public boolean supportsSignature() {
        return usageType == CertificateUsageType.SIGN || usageType == CertificateUsageType.DUAL;
    }
    
    /**
     * 检查模板是否支持加密功能
     * 
     * @return true表示支持加密，false表示不支持
     */
    public boolean supportsEncryption() {
        return usageType == CertificateUsageType.ENCRYPT || usageType == CertificateUsageType.DUAL;
    }

    // ==================== 内部枚举类型 ====================
    
    /**
     * 证书用途类型
     */
    public enum CertificateUsageType {
        /** 仅签名 */
        SIGN,
        /** 仅加密 */
        ENCRYPT,
        /** 签名和加密 */
        DUAL,
        /** 服务器认证 */
        SERVER_AUTH,
        /** 代码签名 */
        CODE_SIGN
    }
    
    /**
     * 证书用户类型
     */
    public enum CertificateUserType {
        /** 个人用户 */
        PERSONAL,
        /** 企业员工 */
        EMPLOYEE,
        /** 组织机构 */
        ORGANIZATION,
        /** 服务器 */
        SERVER
    }
}
