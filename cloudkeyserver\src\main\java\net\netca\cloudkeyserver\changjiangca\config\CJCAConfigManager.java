package net.netca.cloudkeyserver.changjiangca.config;

import lombok.extern.slf4j.Slf4j;
import net.netca.cloudkey.base.util.CommonUtil;
import net.netca.cloudkey.base.util.ConfigKeyValueCacheUtil;
import net.netca.cloudkeyserver.changjiangca.exception.CJCAException;
import net.netca.pki.encoding.asn1.pki.PrivateKeyInfo;
import net.netca.pki.encoding.asn1.pki.X509Certificate;
import net.netca.sdk.message.cmp.config.CmpMessageConfigManagement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

/**
 * 长江CA配置管理器
 * 
 * 负责管理长江CA平台的配置信息，包括：
 * - 基础连接配置（URL、证书等）
 * - CMP协议配置
 * - 超时和重试配置
 * - 配置验证和热更新支持
 * 
 * <AUTHOR> Team
 * @since 2.42.1
 */
@Component
@Slf4j
public class CJCAConfigManager {

    @Autowired
    private ConfigKeyValueCacheUtil configKeyValueCacheUtil;

    // ==================== 配置键常量 ====================
    
    private static final String CJCA_BASE_URL = "CLOUDKEY.CJCA.BASE.URL";
    private static final String CJCA_CA_COMM_CERT_SHA256 = "CLOUDKEY.CJCA.CA.COMM.CERT.SHA256";
    private static final String CJCA_RA_COMM_CERT = "CLOUDKEY.CJCA.RA.COMM.CERT";
    private static final String CJCA_THIRD_PARTY_COMM_CERT = "CLOUDKEY.CJCA.THIRD.PARTY.COMM.CERT";
    private static final String CJCA_THIRD_PARTY_COMM_CERT_KEY = "CLOUDKEY.CJCA.THIRD.PARTY.COMM.CERT.KEY";
    private static final String CJCA_SIGN_ALGO_NAME = "CLOUDKEY.CJCA.SIGN.ALGO.NAME";
    private static final String CJCA_CONNECTION_TIMEOUT = "CLOUDKEY.CJCA.CONNECTION.TIMEOUT";
    private static final String CJCA_READ_TIMEOUT = "CLOUDKEY.CJCA.READ.TIMEOUT";

    // ==================== 配置获取方法 ====================
    
    /**
     * 获取长江CA平台基础URL
     */
    public String getBaseUrl() {
        return configKeyValueCacheUtil.selectConfigValueByKey(CJCA_BASE_URL);
    }

    /**
     * 获取CA通讯证书SHA256指纹
     */
    public String getCaCommCertSha256() {
        return configKeyValueCacheUtil.selectConfigValueByKey(CJCA_CA_COMM_CERT_SHA256);
    }

    /**
     * 获取RA通讯证书
     */
    public String getRaCommCert() {
        return configKeyValueCacheUtil.selectConfigValueByKey(CJCA_RA_COMM_CERT);
    }

    /**
     * 获取第三方通讯证书
     */
    public String getThirdPartyCommCert() {
        return configKeyValueCacheUtil.selectConfigValueByKey(CJCA_THIRD_PARTY_COMM_CERT);
    }

    /**
     * 获取第三方通讯证书私钥
     */
    public String getThirdPartyCommCertKey() {
        return configKeyValueCacheUtil.selectConfigValueByKey(CJCA_THIRD_PARTY_COMM_CERT_KEY);
    }

    /**
     * 获取签名算法名称
     */
    public String getSignAlgoName() {
        String algoName = configKeyValueCacheUtil.selectConfigValueByKey(CJCA_SIGN_ALGO_NAME);
        return CommonUtil.isStringEmpty(algoName) ? "SHA256WithRSA" : algoName;
    }

    /**
     * 获取连接超时时间（毫秒）
     */
    public long getConnectionTimeout() {
        String timeout = configKeyValueCacheUtil.selectConfigValueByKey(CJCA_CONNECTION_TIMEOUT);
        try {
            return CommonUtil.isStringEmpty(timeout) ? 30000L : Long.parseLong(timeout);
        } catch (NumberFormatException e) {
            log.warn("连接超时配置格式错误，使用默认值: {}", timeout);
            return 30000L;
        }
    }

    /**
     * 获取读取超时时间（毫秒）
     */
    public long getReadTimeout() {
        String timeout = configKeyValueCacheUtil.selectConfigValueByKey(CJCA_READ_TIMEOUT);
        try {
            return CommonUtil.isStringEmpty(timeout) ? 60000L : Long.parseLong(timeout);
        } catch (NumberFormatException e) {
            log.warn("读取超时配置格式错误，使用默认值: {}", timeout);
            return 60000L;
        }
    }

    // ==================== 配置验证方法 ====================
    
    /**
     * 验证长江CA配置的完整性
     * 
     * @throws CJCAException 当配置缺失或无效时抛出异常
     */
    public void validateConfiguration() throws CJCAException {
        List<String> missingConfigs = new ArrayList<>();

        if (CommonUtil.isStringEmpty(getBaseUrl())) {
            missingConfigs.add(CJCA_BASE_URL);
        }
        if (CommonUtil.isStringEmpty(getCaCommCertSha256())) {
            missingConfigs.add(CJCA_CA_COMM_CERT_SHA256);
        }
        if (CommonUtil.isStringEmpty(getRaCommCert())) {
            missingConfigs.add(CJCA_RA_COMM_CERT);
        }
        if (CommonUtil.isStringEmpty(getThirdPartyCommCert())) {
            missingConfigs.add(CJCA_THIRD_PARTY_COMM_CERT);
        }
        if (CommonUtil.isStringEmpty(getThirdPartyCommCertKey())) {
            missingConfigs.add(CJCA_THIRD_PARTY_COMM_CERT_KEY);
        }

        if (!missingConfigs.isEmpty()) {
            throw new CJCAException("CJCA_CONFIG_MISSING",
                "缺少必需的长江CA配置项: " + String.join(", ", missingConfigs));
        }

        log.debug("长江CA配置验证通过");
    }

    // ==================== CMP配置创建方法 ====================
    
    /**
     * 创建CMP消息配置管理对象
     *
     * @return CMP消息配置管理对象
     * @throws CJCAException 当配置创建失败时抛出异常
     */
    public CmpMessageConfigManagement createCmpMessageConfigManagement() throws CJCAException {
        try {
            validateConfiguration();

            // 获取Base64编码的配置数据
            String raCommCertBase64 = getRaCommCert();
            String thirdPartyCommCertBase64 = getThirdPartyCommCert();
            String thirdPartyCommCertKeyBase64 = getThirdPartyCommCertKey();
            String signAlgoName = getSignAlgoName();

            // 将Base64字符串转换为对象
            X509Certificate raCommCert = new X509Certificate(raCommCertBase64);
            X509Certificate thirdPartyServerCommCert = new X509Certificate(thirdPartyCommCertBase64);

            // 解码私钥Base64字符串为字节数组，然后转换为PrivateKeyInfo对象
            byte[] thirdPartyCommCertKeyBlob = Base64.getDecoder().decode(thirdPartyCommCertKeyBase64);
            PrivateKeyInfo thirdPartyServerCommCertPrivateKeyInfo = PrivateKeyInfo.decode(thirdPartyCommCertKeyBlob);

            // 使用Builder模式创建CmpMessageConfigManagement实例
            CmpMessageConfigManagement cmpConfig = CmpMessageConfigManagement.builder()
                    .communicationCert(raCommCert)
                    .thirdPartyServerCommCert(thirdPartyServerCommCert)
                    .thirdPartyServerCommCertPrivateKeyInfo(thirdPartyServerCommCertPrivateKeyInfo)
                    .thirdPartyServerCommCertSignAlgoName(signAlgoName)
                    .build();

            log.debug("CMP消息配置管理对象创建成功");
            return cmpConfig;

        } catch (Exception e) {
            log.error("创建CMP消息配置管理对象失败", e);
            throw new CJCAException("CJCA_CONFIG_CREATE_FAILED",
                "创建CMP配置失败: " + e.getMessage(), e);
        }
    }

    // ==================== 配置状态检查方法 ====================
    
    /**
     * 检查长江CA配置是否可用
     * 
     * @return 配置是否可用
     */
    public boolean isConfigurationAvailable() {
        try {
            validateConfiguration();
            return true;
        } catch (Exception e) {
            log.debug("长江CA配置不可用: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取配置状态描述
     * 
     * @return 配置状态描述
     */
    public String getConfigurationStatus() {
        try {
            validateConfiguration();
            return "长江CA配置正常";
        } catch (CJCAException e) {
            return "长江CA配置异常: " + e.getMessage();
        }
    }
}
