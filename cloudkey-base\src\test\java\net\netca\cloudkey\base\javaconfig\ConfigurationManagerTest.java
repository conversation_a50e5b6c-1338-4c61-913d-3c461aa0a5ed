package net.netca.cloudkey.base.javaconfig;

import net.netca.cloudkey.base.exception.ConfigurationException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.env.Environment;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 配置管理器测试类
 *
 * <AUTHOR>
 */
public class ConfigurationManagerTest {

    @Mock
    private Environment environment;

    @Mock
    private CloudKeyConfigProperties configProperties;

    @InjectMocks
    private ConfigurationManager configurationManager;

    @BeforeEach
    public void setUp() throws ConfigurationException {
        MockitoAnnotations.openMocks(this);
        
        // 设置系统属性用于测试
        System.setProperty("cloudkey.shardingsphere.enabled", "true");
        System.setProperty("cloudkey.schedule.auditAppLog.enabled", "true");
        System.setProperty("cloudkey.schedule.auditAppLogTruncate.enabled", "true");
        System.setProperty("cloudkey.schedule.businessCoordSignApiLog.enabled", "true");
        System.setProperty("cloudkey.schedule.businessCoordSignApiLogTruncate.enabled", "true");
        
        // 模拟 CloudKeyConfigProperties
        CloudKeyConfigProperties.ShardingSphereConfig shardingSphereConfig = new CloudKeyConfigProperties.ShardingSphereConfig();
        shardingSphereConfig.setEnabled(true);
        
        CloudKeyConfigProperties.ScheduleConfig scheduleConfig = new CloudKeyConfigProperties.ScheduleConfig();
        CloudKeyConfigProperties.ScheduleConfig.AuditAppLogConfig auditAppLogConfig = new CloudKeyConfigProperties.ScheduleConfig.AuditAppLogConfig();
        auditAppLogConfig.setEnabled(true);
        scheduleConfig.setAuditAppLog(auditAppLogConfig);
        
        CloudKeyConfigProperties.ScheduleConfig.AuditAppLogTruncateConfig auditAppLogTruncateConfig = new CloudKeyConfigProperties.ScheduleConfig.AuditAppLogTruncateConfig();
        auditAppLogTruncateConfig.setEnabled(true);
        scheduleConfig.setAuditAppLogTruncate(auditAppLogTruncateConfig);
        
        CloudKeyConfigProperties.ScheduleConfig.BusinessCoordSignApiLogConfig businessCoordSignApiLogConfig = new CloudKeyConfigProperties.ScheduleConfig.BusinessCoordSignApiLogConfig();
        businessCoordSignApiLogConfig.setEnabled(true);
        scheduleConfig.setBusinessCoordSignApiLog(businessCoordSignApiLogConfig);
        
        CloudKeyConfigProperties.ScheduleConfig.BusinessCoordSignApiLogTruncateConfig businessCoordSignApiLogTruncateConfig = new CloudKeyConfigProperties.ScheduleConfig.BusinessCoordSignApiLogTruncateConfig();
        businessCoordSignApiLogTruncateConfig.setEnabled(true);
        scheduleConfig.setBusinessCoordSignApiLogTruncate(businessCoordSignApiLogTruncateConfig);
        
        when(configProperties.getShardingsphere()).thenReturn(shardingSphereConfig);
        when(configProperties.getSchedule()).thenReturn(scheduleConfig);
        
        doNothing().when(configProperties).validate();
    }

    @Test
    public void testInit() throws ConfigurationException {
        // 执行初始化
        assertDoesNotThrow(() -> configurationManager.init());
        
        // 验证方法调用
        verify(configProperties, times(1)).validate();
    }

    @Test
    public void testReloadConfigurations() throws ConfigurationException {
        // 执行重新加载
        assertDoesNotThrow(() -> configurationManager.reloadConfigurations());
        
        // 验证方法调用
        verify(configProperties, times(1)).validate();
    }

    @Test
    public void testInitWithValidationFailure() throws ConfigurationException {
        // 模拟验证失败
        doThrow(new ConfigurationException("test.key", "test.value", "Test validation error"))
                .when(configProperties).validate();
        
        // 执行初始化应该不会抛出异常，但会记录错误日志
        assertDoesNotThrow(() -> configurationManager.init());
        
        // 验证方法调用
        verify(configProperties, times(1)).validate();
    }
}