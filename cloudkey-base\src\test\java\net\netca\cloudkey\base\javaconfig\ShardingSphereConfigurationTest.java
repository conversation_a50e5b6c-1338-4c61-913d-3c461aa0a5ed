package net.netca.cloudkey.base.javaconfig;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.env.Environment;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * ShardingSphere配置测试类
 *
 * <AUTHOR>
 */
public class ShardingSphereConfigurationTest {

    @Mock
    private Environment environment;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testShardingSphereEnabledByDefault() {
        // 准备测试数据
        CloudKeyConfigProperties properties = new CloudKeyConfigProperties();
        
        // 验证默认值
        assertTrue(properties.getShardingsphere().isEnabled(), "ShardingSphere should be enabled by default");
    }

    @Test
    public void testShardingSphereDisabled() {
        // 准备测试数据
        CloudKeyConfigProperties properties = new CloudKeyConfigProperties();
        properties.getShardingsphere().setEnabled(false);
        
        // 验证设置值
        assertFalse(properties.getShardingsphere().isEnabled(), "ShardingSphere should be disabled");
    }

    @Test
    public void testConfigurationReader() {
        // 设置系统属性
        System.setProperty("test.property", "test-value");
        
        // 模拟环境变量
        when(environment.getProperty("test.env.property")).thenReturn("env-value");
        
        // 初始化配置读取器
        ConfigurationReader.initialize(environment);
        
        // 验证从系统属性读取
        assertEquals("test-value", ConfigurationReader.getString("test.property", "default"), 
                "Should read value from system property");
        
        // 验证从环境变量读取
        assertEquals("env-value", ConfigurationReader.getString("test.env.property", "default"), 
                "Should read value from environment");
        
        // 验证默认值
        assertEquals("default", ConfigurationReader.getString("non.existent.property", "default"), 
                "Should return default value for non-existent property");
        
        // 清理系统属性
        System.clearProperty("test.property");
    }

    @Test
    public void testBooleanConfigurationReader() {
        // 设置系统属性
        System.setProperty("test.boolean.true", "true");
        System.setProperty("test.boolean.false", "false");
        System.setProperty("test.boolean.invalid", "not-a-boolean");
        
        // 初始化配置读取器
        ConfigurationReader.initialize(environment);
        
        // 验证布尔值读取
        assertTrue(ConfigurationReader.getBoolean("test.boolean.true", false), 
                "Should read true value");
        
        assertFalse(ConfigurationReader.getBoolean("test.boolean.false", true), 
                "Should read false value");
        
        // 验证无效布尔值使用默认值
        assertTrue(ConfigurationReader.getBoolean("test.boolean.invalid", true), 
                "Should use default value for invalid boolean");
        
        // 清理系统属性
        System.clearProperty("test.boolean.true");
        System.clearProperty("test.boolean.false");
        System.clearProperty("test.boolean.invalid");
    }
}