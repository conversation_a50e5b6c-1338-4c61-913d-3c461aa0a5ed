package net.netca.cloudkeyserver.changjiangca.exception;

/**
 * 长江CA配置相关异常类
 *
 * 用于处理长江CA平台配置相关的异常情况，包括：
 * - 配置项缺失
 * - 配置项格式错误
 * - 配置项验证失败
 * - 证书配置错误
 * - 模板配置错误
 *
 * <AUTHOR> Team
 * @since 2.42.1
 */
public class CJCAConfigException extends CJCAException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 构造方法
     *
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     */
    public CJCAConfigException(String errorCode, String errorMessage) {
        super(errorCode, errorMessage);
    }

    /**
     * 构造方法（带原因异常）
     *
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @param cause 原因异常
     */
    public CJCAConfigException(String errorCode, String errorMessage, Throwable cause) {
        super(errorCode, errorMessage, cause);
    }

    /**
     * 构造方法（仅错误消息）
     *
     * @param errorMessage 错误消息
     */
    public CJCAConfigException(String errorMessage) {
        super("CJCA_CONFIG_ERROR", errorMessage);
    }

    /**
     * 构造方法（仅错误消息和原因异常）
     *
     * @param errorMessage 错误消息
     * @param cause 原因异常
     */
    public CJCAConfigException(String errorMessage, Throwable cause) {
        super("CJCA_CONFIG_ERROR", errorMessage, cause);
    }
    
    /**
     * 创建配置项缺失异常
     *
     * @param configKey 缺失的配置项键名
     * @return CjcaConfigException实例
     */
    public static CJCAConfigException missingConfig(String configKey) {
        return new CJCAConfigException("CJCA_CONFIG_MISSING",
            "缺少必需的配置项: " + configKey);
    }

    /**
     * 创建配置项无效异常
     *
     * @param configKey 无效的配置项键名
     * @param reason 无效原因
     * @return CjcaConfigException实例
     */
    public static CJCAConfigException invalidConfig(String configKey, String reason) {
        return new CJCAConfigException("CJCA_CONFIG_INVALID",
            "配置项 " + configKey + " 无效: " + reason);
    }

    /**
     * 创建证书配置错误异常
     *
     * @param certType 证书类型
     * @param reason 错误原因
     * @return CjcaConfigException实例
     */
    public static CJCAConfigException certificateConfigError(String certType, String reason) {
        return new CJCAConfigException("CJCA_CERT_CONFIG_ERROR",
            "证书配置错误 (" + certType + "): " + reason);
    }

    /**
     * 创建证书配置错误异常（带原因异常）
     *
     * @param certType 证书类型
     * @param reason 错误原因
     * @param cause 原因异常
     * @return CjcaConfigException实例
     */
    public static CJCAConfigException certificateConfigError(String certType, String reason, Throwable cause) {
        return new CJCAConfigException("CJCA_CERT_CONFIG_ERROR",
            "证书配置错误 (" + certType + "): " + reason, cause);
    }
    
    /**
     * 创建模板配置错误异常
     *
     * @param templateId 模板ID
     * @param reason 错误原因
     * @return CjcaConfigException实例
     */
    public static CJCAConfigException templateConfigError(String templateId, String reason) {
        return new CJCAConfigException("CJCA_TEMPLATE_CONFIG_ERROR",
            "证书模板配置错误 (模板ID: " + templateId + "): " + reason);
    }

    /**
     * 创建算法配置错误异常
     *
     * @param algorithm 算法名称
     * @param reason 错误原因
     * @return CjcaConfigException实例
     */
    public static CJCAConfigException algorithmConfigError(String algorithm, String reason) {
        return new CJCAConfigException("CJCA_ALGORITHM_CONFIG_ERROR",
            "签名算法配置错误 (" + algorithm + "): " + reason);
    }

    /**
     * 创建超时配置错误异常
     *
     * @param timeoutType 超时类型
     * @param value 配置值
     * @param reason 错误原因
     * @return CjcaConfigException实例
     */
    public static CJCAConfigException timeoutConfigError(String timeoutType, String value, String reason) {
        return new CJCAConfigException("CJCA_TIMEOUT_CONFIG_ERROR",
            "超时配置错误 (" + timeoutType + "=" + value + "): " + reason);
    }
}
