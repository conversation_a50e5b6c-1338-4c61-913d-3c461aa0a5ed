package net.netca.cloudkeyserver.manager;

import net.netca.cloudkey.base.po.BusinessUser;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.pojo.resp.NetcaBpmsResponse;
import net.netca.cloudkeyserver.changjiangca.manager.CJCACertificateLifecycleManager;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * updateBusinessInfo 重构验证测试
 * 
 * 验证模板方法模式重构后的功能正确性
 */
@ExtendWith(MockitoExtension.class)
public class UpdateBusinessInfoRefactoringTest {

    @Mock
    private NetcaBpmsResponse mockBpmsResponse;

    @Mock
    private BusinessUser mockBusinessUser;

    /**
     * 测试 DefaultCertificateLifecycleManager 的抽象方法实现
     */
    @Test
    public void testDefaultCertificateLifecycleManagerAbstractMethods() {
        // 创建一个测试用的 DefaultCertificateLifecycleManager 实例
        TestableDefaultCertificateLifecycleManager manager = new TestableDefaultCertificateLifecycleManager();

        // 测试响应状态映射
        when(mockBpmsResponse.getStatus()).thenReturn(0L); // 成功状态
        AbstractCertificateLifecycleManager.BusinessResponseStatus status = manager.mapResponseStatus(mockBpmsResponse);
        assertEquals(AbstractCertificateLifecycleManager.BusinessResponseStatus.SUCCESS, status);

        when(mockBpmsResponse.getStatus()).thenReturn(1L); // 待审核状态
        status = manager.mapResponseStatus(mockBpmsResponse);
        assertEquals(AbstractCertificateLifecycleManager.BusinessResponseStatus.PENDING, status);

        when(mockBpmsResponse.getStatus()).thenReturn(999L); // 失败状态
        status = manager.mapResponseStatus(mockBpmsResponse);
        assertEquals(AbstractCertificateLifecycleManager.BusinessResponseStatus.FAILURE, status);
    }


    /**
     * 可测试的 DefaultCertificateLifecycleManager 实现
     */
    private static class TestableDefaultCertificateLifecycleManager extends DefaultCertificateLifecycleManager {
        // 重写需要依赖注入的方法，避免在测试中出现空指针异常
        @Override
        protected Map<String, Object> parseResponseData(NetcaBpmsResponse bpmsResponse) throws Exception {
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("status", bpmsResponse.getStatus());
            return responseData;
        }

        @Override
        protected Map<String, Map<String, Object>> extractCertificateInfo(Map<String, Object> responseData) throws Exception {
            return new HashMap<>();
        }
    }

    /**
     * 可测试的 CJCACertificateLifecycleManager 实现
     */
    private static class TestableCJCACertificateLifecycleManager extends CJCACertificateLifecycleManager {
        // 重写需要依赖注入的方法，避免在测试中出现空指针异常
        @Override
        protected Map<String, Object> parseResponseData(NetcaBpmsResponse bpmsResponse) throws Exception {
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("status", bpmsResponse.getStatus());
            return responseData;
        }

        @Override
        protected Map<String, Map<String, Object>> extractCertificateInfo(Map<String, Object> responseData) throws Exception {
            return new HashMap<>();
        }
    }
}
