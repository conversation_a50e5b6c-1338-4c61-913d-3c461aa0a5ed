package net.netca.cloudkey.base.javaconfig;

import net.netca.cloudkey.base.exception.ConfigurationException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * CloudKeyConfigProperties 测试类
 * 验证配置属性类的功能
 *
 * <AUTHOR>
 */
public class CloudKeyConfigPropertiesTest {

    private CloudKeyConfigProperties configProperties;

    @BeforeEach
    void setUp() {
        configProperties = new CloudKeyConfigProperties();
    }

    @Test
    void testDefaultConfiguration() {
        // 验证默认配置
        assertTrue(configProperties.getShardingsphere().isEnabled());
        assertTrue(configProperties.getSchedule().getAuditAppLog().isEnabled());
        assertTrue(configProperties.getSchedule().getAuditAppLogTruncate().isEnabled());
        assertTrue(configProperties.getSchedule().getBusinessCoordSignApiLog().isEnabled());
        assertTrue(configProperties.getSchedule().getBusinessCoordSignApiLogTruncate().isEnabled());
    }

    @Test
    void testShardingSphereConfiguration() {
        CloudKeyConfigProperties.ShardingSphereConfig config = new CloudKeyConfigProperties.ShardingSphereConfig();
        
        // 测试默认值
        assertTrue(config.isEnabled());
        
        // 测试设置值
        config.setEnabled(false);
        assertFalse(config.isEnabled());
    }

    @Test
    void testScheduleConfiguration() {
        CloudKeyConfigProperties.ScheduleConfig scheduleConfig = new CloudKeyConfigProperties.ScheduleConfig();
        
        // 验证默认配置
        assertNotNull(scheduleConfig.getAuditAppLog());
        assertNotNull(scheduleConfig.getAuditAppLogTruncate());
        assertNotNull(scheduleConfig.getBusinessCoordSignApiLog());
        assertNotNull(scheduleConfig.getBusinessCoordSignApiLogTruncate());
        
        assertTrue(scheduleConfig.getAuditAppLog().isEnabled());
        assertTrue(scheduleConfig.getAuditAppLogTruncate().isEnabled());
        assertTrue(scheduleConfig.getBusinessCoordSignApiLog().isEnabled());
        assertTrue(scheduleConfig.getBusinessCoordSignApiLogTruncate().isEnabled());
    }

    @Test
    void testAuditAppLogEffectivelyEnabled() {
        try (MockedStatic<ConfigurationReader> mockedReader = Mockito.mockStatic(ConfigurationReader.class)) {
            CloudKeyConfigProperties.ScheduleConfig.AuditAppLogConfig config = 
                new CloudKeyConfigProperties.ScheduleConfig.AuditAppLogConfig();
            
            // 测试 ShardingSphere 启用时
            mockedReader.when(() -> ConfigurationReader.getBoolean("cloudkey.shardingsphere.enabled", true))
                       .thenReturn(true);
            assertTrue(config.isEffectivelyEnabled());
            
            // 测试 ShardingSphere 禁用时
            mockedReader.when(() -> ConfigurationReader.getBoolean("cloudkey.shardingsphere.enabled", true))
                       .thenReturn(false);
            assertFalse(config.isEffectivelyEnabled());
            
            // 测试任务本身禁用时
            config.setEnabled(false);
            mockedReader.when(() -> ConfigurationReader.getBoolean("cloudkey.shardingsphere.enabled", true))
                       .thenReturn(true);
            assertFalse(config.isEffectivelyEnabled());
        }
    }

    @Test
    void testAuditAppLogTruncateEffectivelyEnabled() {
        try (MockedStatic<ConfigurationReader> mockedReader = Mockito.mockStatic(ConfigurationReader.class)) {
            CloudKeyConfigProperties.ScheduleConfig.AuditAppLogTruncateConfig config = 
                new CloudKeyConfigProperties.ScheduleConfig.AuditAppLogTruncateConfig();
            
            // 测试 ShardingSphere 启用时
            mockedReader.when(() -> ConfigurationReader.getBoolean("cloudkey.shardingsphere.enabled", true))
                       .thenReturn(true);
            assertTrue(config.isEffectivelyEnabled());
            
            // 测试 ShardingSphere 禁用时
            mockedReader.when(() -> ConfigurationReader.getBoolean("cloudkey.shardingsphere.enabled", true))
                       .thenReturn(false);
            assertFalse(config.isEffectivelyEnabled());
        }
    }

    @Test
    void testBusinessCoordSignApiLogEffectivelyEnabled() {
        try (MockedStatic<ConfigurationReader> mockedReader = Mockito.mockStatic(ConfigurationReader.class)) {
            CloudKeyConfigProperties.ScheduleConfig.BusinessCoordSignApiLogConfig config = 
                new CloudKeyConfigProperties.ScheduleConfig.BusinessCoordSignApiLogConfig();
            
            // 测试 ShardingSphere 启用时
            mockedReader.when(() -> ConfigurationReader.getBoolean("cloudkey.shardingsphere.enabled", true))
                       .thenReturn(true);
            assertTrue(config.isEffectivelyEnabled());
            
            // 测试 ShardingSphere 禁用时
            mockedReader.when(() -> ConfigurationReader.getBoolean("cloudkey.shardingsphere.enabled", true))
                       .thenReturn(false);
            assertFalse(config.isEffectivelyEnabled());
        }
    }

    @Test
    void testBusinessCoordSignApiLogTruncateEffectivelyEnabled() {
        try (MockedStatic<ConfigurationReader> mockedReader = Mockito.mockStatic(ConfigurationReader.class)) {
            CloudKeyConfigProperties.ScheduleConfig.BusinessCoordSignApiLogTruncateConfig config = 
                new CloudKeyConfigProperties.ScheduleConfig.BusinessCoordSignApiLogTruncateConfig();
            
            // 测试 ShardingSphere 启用时
            mockedReader.when(() -> ConfigurationReader.getBoolean("cloudkey.shardingsphere.enabled", true))
                       .thenReturn(true);
            assertTrue(config.isEffectivelyEnabled());
            
            // 测试 ShardingSphere 禁用时
            mockedReader.when(() -> ConfigurationReader.getBoolean("cloudkey.shardingsphere.enabled", true))
                       .thenReturn(false);
            assertFalse(config.isEffectivelyEnabled());
        }
    }

    @Test
    void testConfigurationValidation() {
        try (MockedStatic<ConfigurationReader> mockedReader = Mockito.mockStatic(ConfigurationReader.class)) {
            // Mock 配置读取
            mockedReader.when(() -> ConfigurationReader.getString(anyString(), anyString()))
                       .thenReturn("test-value");
            mockedReader.when(() -> ConfigurationReader.getBoolean(anyString(), anyBoolean()))
                       .thenReturn(true);
            
            // 测试配置验证不抛出异常
            assertDoesNotThrow(() -> configProperties.validate());
        }
    }

    @Test
    void testConfigurationMerge() {
        CloudKeyConfigProperties other = new CloudKeyConfigProperties();
        other.getShardingsphere().setEnabled(false);
        other.getSchedule().getAuditAppLog().setEnabled(false);
        
        configProperties.merge(other);
        
        // 验证合并结果
        assertFalse(configProperties.getShardingsphere().isEnabled());
        assertFalse(configProperties.getSchedule().getAuditAppLog().isEnabled());
    }

    @Test
    void testMergeWithNullConfiguration() {
        CloudKeyConfigProperties original = new CloudKeyConfigProperties();
        boolean originalShardingSphereEnabled = original.getShardingsphere().isEnabled();
        
        // 合并 null 配置应该不改变原配置
        original.merge(null);
        
        assertEquals(originalShardingSphereEnabled, original.getShardingsphere().isEnabled());
    }
}