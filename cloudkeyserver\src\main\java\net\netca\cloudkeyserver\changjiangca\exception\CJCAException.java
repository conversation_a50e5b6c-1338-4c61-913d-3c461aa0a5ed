package net.netca.cloudkeyserver.changjiangca.exception;

/**
 * 长江CA平台异常基础类
 *
 * 用于封装长江CA平台对接过程中发生的各种异常情况，
 * 提供统一的异常处理机制和错误信息管理。
 *
 * <AUTHOR> Team
 * @since 2.42.1
 */
public class CJCAException extends Exception {

    private static final long serialVersionUID = 1L;

    /** 错误代码 */
    private final String errorCode;

    /** 错误消息 */
    private final String errorMessage;

    /**
     * 构造方法
     *
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     */
    public CJCAException(String errorCode, String errorMessage) {
        super(errorMessage);
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    /**
     * 构造方法（带原因异常）
     *
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @param cause 原因异常
     */
    public CJCAException(String errorCode, String errorMessage, Throwable cause) {
        super(errorMessage, cause);
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    /**
     * 构造方法（仅错误消息）
     *
     * @param errorMessage 错误消息
     */
    public CJCAException(String errorMessage) {
        super(errorMessage);
        this.errorCode = "CJCA_UNKNOWN_ERROR";
        this.errorMessage = errorMessage;
    }

    /**
     * 构造方法（仅错误消息和原因异常）
     *
     * @param errorMessage 错误消息
     * @param cause 原因异常
     */
    public CJCAException(String errorMessage, Throwable cause) {
        super(errorMessage, cause);
        this.errorCode = "CJCA_UNKNOWN_ERROR";
        this.errorMessage = errorMessage;
    }

    /**
     * 获取错误代码
     *
     * @return 错误代码
     */
    public String getErrorCode() {
        return errorCode;
    }

    /**
     * 获取错误消息
     *
     * @return 错误消息
     */
    public String getErrorMessage() {
        return errorMessage;
    }

    /**
     * 重写toString方法，提供详细的异常信息
     *
     * @return 格式化的异常信息
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName()).append(": ");
        if (errorCode != null) {
            sb.append("[").append(errorCode).append("] ");
        }
        sb.append(errorMessage != null ? errorMessage : getMessage());

        // 如果有原因异常，添加原因信息
        if (getCause() != null) {
            sb.append(" - 原因: ").append(getCause().getMessage());
        }

        return sb.toString();
    }

    /**
     * 创建配置相关异常的便捷方法
     *
     * @param message 错误消息
     * @return CJCAException 实例
     */
    public static CJCAException configError(String message) {
        return new CJCAException("CJCA_CONFIG_ERROR", message);
    }

    /**
     * 创建配置相关异常的便捷方法（带原因异常）
     *
     * @param message 错误消息
     * @param cause 原因异常
     * @return CJCAException 实例
     */
    public static CJCAException configError(String message, Throwable cause) {
        return new CJCAException("CJCA_CONFIG_ERROR", message, cause);
    }

    /**
     * 创建连接相关异常的便捷方法
     *
     * @param message 错误消息
     * @return CCJCAException 实例
     */
    public static CJCAException connectionError(String message) {
        return new CJCAException("CJCA_CONNECTION_ERROR", message);
    }

    /**
     * 创建连接相关异常的便捷方法（带原因异常）
     *
     * @param message 错误消息
     * @param cause 原因异常
     * @return CJCAException 实例
     */
    public static CJCAException connectionError(String message, Throwable cause) {
        return new CJCAException("CJCA_CONNECTION_ERROR", message, cause);
    }

    /**
     * 创建CMP协议相关异常的便捷方法
     *
     * @param message 错误消息
     * @return CJCAException 实例
     */
    public static CJCAException cmpProtocolError(String message) {
        return new CJCAException("CJCA_CMP_PROTOCOL_ERROR", message);
    }

    /**
     * 创建CMP协议相关异常的便捷方法（带原因异常）
     *
     * @param message 错误消息
     * @param cause 原因异常
     * @return CJCAException 实例
     */
    public static CJCAException cmpProtocolError(String message, Throwable cause) {
        return new CJCAException("CJCA_CMP_PROTOCOL_ERROR", message, cause);
    }
}
